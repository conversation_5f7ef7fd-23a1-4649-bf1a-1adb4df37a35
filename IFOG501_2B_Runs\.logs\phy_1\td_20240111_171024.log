============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 17:10:24 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1529 instances
RUN-0007 : 369 luts, 899 seqs, 137 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2082 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1536 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1527 instances, 369 luts, 899 seqs, 212 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7369, tnet num: 2080, tinst num: 1527, tnode num: 10352, tedge num: 12518.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.345894s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (103.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 570429
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1527.
PHY-3001 : End clustering;  0.000028s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 502844, overlap = 13.5
PHY-3002 : Step(2): len = 468636, overlap = 15.75
PHY-3002 : Step(3): len = 450044, overlap = 18
PHY-3002 : Step(4): len = 438549, overlap = 13.5
PHY-3002 : Step(5): len = 421555, overlap = 20.25
PHY-3002 : Step(6): len = 405639, overlap = 13.5
PHY-3002 : Step(7): len = 396095, overlap = 13.5
PHY-3002 : Step(8): len = 388066, overlap = 13.5
PHY-3002 : Step(9): len = 370722, overlap = 15.75
PHY-3002 : Step(10): len = 362151, overlap = 15.75
PHY-3002 : Step(11): len = 354962, overlap = 13.5
PHY-3002 : Step(12): len = 344018, overlap = 15.75
PHY-3002 : Step(13): len = 335189, overlap = 15.75
PHY-3002 : Step(14): len = 329133, overlap = 20.25
PHY-3002 : Step(15): len = 320089, overlap = 15.75
PHY-3002 : Step(16): len = 312167, overlap = 20.25
PHY-3002 : Step(17): len = 305475, overlap = 18
PHY-3002 : Step(18): len = 298205, overlap = 20.25
PHY-3002 : Step(19): len = 288314, overlap = 15.75
PHY-3002 : Step(20): len = 282873, overlap = 18
PHY-3002 : Step(21): len = 276888, overlap = 15.75
PHY-3002 : Step(22): len = 263724, overlap = 18
PHY-3002 : Step(23): len = 254978, overlap = 15.75
PHY-3002 : Step(24): len = 251803, overlap = 18
PHY-3002 : Step(25): len = 239247, overlap = 15.75
PHY-3002 : Step(26): len = 213595, overlap = 18
PHY-3002 : Step(27): len = 207534, overlap = 15.75
PHY-3002 : Step(28): len = 205085, overlap = 18
PHY-3002 : Step(29): len = 164818, overlap = 13.5
PHY-3002 : Step(30): len = 159390, overlap = 13.5
PHY-3002 : Step(31): len = 157840, overlap = 13.5
PHY-3002 : Step(32): len = 152706, overlap = 18
PHY-3002 : Step(33): len = 148146, overlap = 15.75
PHY-3002 : Step(34): len = 145376, overlap = 15.75
PHY-3002 : Step(35): len = 142554, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000115114
PHY-3002 : Step(36): len = 142843, overlap = 13.5
PHY-3002 : Step(37): len = 141700, overlap = 15.75
PHY-3002 : Step(38): len = 140238, overlap = 13.5
PHY-3002 : Step(39): len = 138498, overlap = 13.5
PHY-3002 : Step(40): len = 135490, overlap = 13.5
PHY-3002 : Step(41): len = 130042, overlap = 13.5
PHY-3002 : Step(42): len = 128912, overlap = 11.25
PHY-3002 : Step(43): len = 127525, overlap = 11.25
PHY-3002 : Step(44): len = 123854, overlap = 11.25
PHY-3002 : Step(45): len = 120610, overlap = 11.25
PHY-3002 : Step(46): len = 119068, overlap = 11.25
PHY-3002 : Step(47): len = 117755, overlap = 11.25
PHY-3002 : Step(48): len = 115281, overlap = 13.5
PHY-3002 : Step(49): len = 112028, overlap = 13.5
PHY-3002 : Step(50): len = 111038, overlap = 13.5
PHY-3002 : Step(51): len = 109362, overlap = 13.5
PHY-3002 : Step(52): len = 105645, overlap = 11.25
PHY-3002 : Step(53): len = 102415, overlap = 11.25
PHY-3002 : Step(54): len = 101137, overlap = 11.25
PHY-3002 : Step(55): len = 98579.7, overlap = 15.75
PHY-3002 : Step(56): len = 96988.7, overlap = 18
PHY-3002 : Step(57): len = 95190, overlap = 18
PHY-3002 : Step(58): len = 92704.5, overlap = 15.75
PHY-3002 : Step(59): len = 89328.9, overlap = 11.25
PHY-3002 : Step(60): len = 88449.6, overlap = 11.25
PHY-3002 : Step(61): len = 85939, overlap = 13.5
PHY-3002 : Step(62): len = 84659.4, overlap = 13.5
PHY-3002 : Step(63): len = 83437.5, overlap = 13.5
PHY-3002 : Step(64): len = 81912.8, overlap = 15.75
PHY-3002 : Step(65): len = 78899, overlap = 11.25
PHY-3002 : Step(66): len = 77965.8, overlap = 9
PHY-3002 : Step(67): len = 76236.1, overlap = 13.5
PHY-3002 : Step(68): len = 74622.6, overlap = 13.5
PHY-3002 : Step(69): len = 73413.3, overlap = 15.75
PHY-3002 : Step(70): len = 72028.5, overlap = 11.5
PHY-3002 : Step(71): len = 70591.8, overlap = 11.6875
PHY-3002 : Step(72): len = 69794.4, overlap = 14.3125
PHY-3002 : Step(73): len = 68813.5, overlap = 16.875
PHY-3002 : Step(74): len = 67398.1, overlap = 14.875
PHY-3002 : Step(75): len = 66825.2, overlap = 13.125
PHY-3002 : Step(76): len = 65725.9, overlap = 13.3125
PHY-3002 : Step(77): len = 65180.3, overlap = 11
PHY-3002 : Step(78): len = 64765.6, overlap = 11.1875
PHY-3002 : Step(79): len = 63043.5, overlap = 16.3125
PHY-3002 : Step(80): len = 61587.3, overlap = 13.6875
PHY-3002 : Step(81): len = 60711.7, overlap = 15.625
PHY-3002 : Step(82): len = 60268, overlap = 15.5
PHY-3002 : Step(83): len = 59978.9, overlap = 15.75
PHY-3002 : Step(84): len = 59446.6, overlap = 13.875
PHY-3002 : Step(85): len = 59278.1, overlap = 13.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000230228
PHY-3002 : Step(86): len = 59241.5, overlap = 13.625
PHY-3002 : Step(87): len = 59082.1, overlap = 15.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000460456
PHY-3002 : Step(88): len = 59179.6, overlap = 15.9375
PHY-3002 : Step(89): len = 59145.8, overlap = 15.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007547s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (207.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062134s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000277373
PHY-3002 : Step(90): len = 61459.5, overlap = 16.3438
PHY-3002 : Step(91): len = 61624.8, overlap = 16.0312
PHY-3002 : Step(92): len = 60424.1, overlap = 15.7812
PHY-3002 : Step(93): len = 60498.3, overlap = 15.5312
PHY-3002 : Step(94): len = 59496.8, overlap = 14.8438
PHY-3002 : Step(95): len = 58377.4, overlap = 15.2188
PHY-3002 : Step(96): len = 57488.9, overlap = 15.5312
PHY-3002 : Step(97): len = 56652.4, overlap = 16.0938
PHY-3002 : Step(98): len = 55203.3, overlap = 16.5312
PHY-3002 : Step(99): len = 54201.6, overlap = 14.9688
PHY-3002 : Step(100): len = 53898.2, overlap = 15.1562
PHY-3002 : Step(101): len = 53100.9, overlap = 12.4062
PHY-3002 : Step(102): len = 52863.1, overlap = 12.7812
PHY-3002 : Step(103): len = 52864, overlap = 12.8125
PHY-3002 : Step(104): len = 52004.2, overlap = 13.125
PHY-3002 : Step(105): len = 51329.4, overlap = 15.75
PHY-3002 : Step(106): len = 50556.9, overlap = 17.2188
PHY-3002 : Step(107): len = 49548.9, overlap = 17.8438
PHY-3002 : Step(108): len = 49105.1, overlap = 17.9688
PHY-3002 : Step(109): len = 48712.9, overlap = 18.3438
PHY-3002 : Step(110): len = 48522.5, overlap = 18.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000554747
PHY-3002 : Step(111): len = 48245.7, overlap = 18.9062
PHY-3002 : Step(112): len = 48238.2, overlap = 19.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00110949
PHY-3002 : Step(113): len = 48216.2, overlap = 19.0312
PHY-3002 : Step(114): len = 48263.3, overlap = 19.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069825s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.05374e-05
PHY-3002 : Step(115): len = 48559.1, overlap = 48.6562
PHY-3002 : Step(116): len = 49020.4, overlap = 48.125
PHY-3002 : Step(117): len = 49472.8, overlap = 48.2188
PHY-3002 : Step(118): len = 49079.3, overlap = 48.1875
PHY-3002 : Step(119): len = 49069.6, overlap = 48.2188
PHY-3002 : Step(120): len = 49069.6, overlap = 48.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000141075
PHY-3002 : Step(121): len = 49082.9, overlap = 42.375
PHY-3002 : Step(122): len = 49682.2, overlap = 43.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00028215
PHY-3002 : Step(123): len = 50454.4, overlap = 40.75
PHY-3002 : Step(124): len = 51396.1, overlap = 39.5938
PHY-3002 : Step(125): len = 53023.3, overlap = 37.6562
PHY-3002 : Step(126): len = 52318.9, overlap = 37.1875
PHY-3002 : Step(127): len = 52095.7, overlap = 36.6562
PHY-3002 : Step(128): len = 51889.3, overlap = 36
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000564299
PHY-3002 : Step(129): len = 52037.4, overlap = 34.9062
PHY-3002 : Step(130): len = 52413.6, overlap = 34.4062
PHY-3002 : Step(131): len = 54005.7, overlap = 27.1875
PHY-3002 : Step(132): len = 54055.3, overlap = 27.7812
PHY-3002 : Step(133): len = 53865.8, overlap = 25.8125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7369, tnet num: 2080, tinst num: 1527, tnode num: 10352, tedge num: 12518.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.12 peak overflow 2.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57304, over cnt = 246(0%), over = 1001, worst = 19
PHY-1001 : End global iterations;  0.090478s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (103.6%)

PHY-1001 : Congestion index: top1 = 43.25, top5 = 25.99, top10 = 16.57, top15 = 11.76.
PHY-1001 : End incremental global routing;  0.144480s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (97.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071025s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.247428s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (101.0%)

OPT-1001 : Current memory(MB): used = 210, reserve = 173, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1601/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57304, over cnt = 246(0%), over = 1001, worst = 19
PHY-1002 : len = 62008, over cnt = 176(0%), over = 485, worst = 14
PHY-1002 : len = 66488, over cnt = 53(0%), over = 133, worst = 12
PHY-1002 : len = 68032, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 68232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099477s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (110.0%)

PHY-1001 : Congestion index: top1 = 36.81, top5 = 25.18, top10 = 18.07, top15 = 13.30.
OPT-1001 : End congestion update;  0.146125s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061271s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.211342s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (103.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 176, peak = 213.
OPT-1001 : End physical optimization;  0.769409s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (103.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 163 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 623 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 992/1287 primitive instances ...
PHY-3001 : End packing;  0.050029s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 786 instances
RUN-1001 : 369 mslices, 368 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1385 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 784 instances, 737 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53838.2, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6251, tnet num: 1925, tinst num: 784, tnode num: 8426, tedge num: 11020.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.319431s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.53083e-05
PHY-3002 : Step(134): len = 53170, overlap = 54.25
PHY-3002 : Step(135): len = 52497.1, overlap = 54.25
PHY-3002 : Step(136): len = 51849.6, overlap = 52
PHY-3002 : Step(137): len = 51796.5, overlap = 51.5
PHY-3002 : Step(138): len = 51567, overlap = 52
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.06166e-05
PHY-3002 : Step(139): len = 51992.3, overlap = 52.5
PHY-3002 : Step(140): len = 53249.6, overlap = 46.75
PHY-3002 : Step(141): len = 53502.4, overlap = 46
PHY-3002 : Step(142): len = 53027.8, overlap = 47.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000141233
PHY-3002 : Step(143): len = 53841.6, overlap = 45.75
PHY-3002 : Step(144): len = 54718.5, overlap = 43.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.089371s wall, 0.109375s user + 0.109375s system = 0.218750s CPU (244.8%)

PHY-3001 : Trial Legalized: Len = 65513.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055808s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000781567
PHY-3002 : Step(145): len = 62562, overlap = 5.75
PHY-3002 : Step(146): len = 60429.4, overlap = 12.75
PHY-3002 : Step(147): len = 59073.5, overlap = 16
PHY-3002 : Step(148): len = 58545.3, overlap = 17.75
PHY-3002 : Step(149): len = 58135.1, overlap = 20.5
PHY-3002 : Step(150): len = 57731.3, overlap = 21.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00149752
PHY-3002 : Step(151): len = 57897.3, overlap = 21
PHY-3002 : Step(152): len = 58121.6, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00299504
PHY-3002 : Step(153): len = 58212.2, overlap = 22.75
PHY-3002 : Step(154): len = 58309.7, overlap = 22.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005925s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (263.7%)

PHY-3001 : Legalized: Len = 62324.9, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006424s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 2, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 62438.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6251, tnet num: 1925, tinst num: 784, tnode num: 8426, tedge num: 11020.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 54/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69008, over cnt = 162(0%), over = 241, worst = 6
PHY-1002 : len = 69888, over cnt = 90(0%), over = 114, worst = 3
PHY-1002 : len = 71360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134827s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (150.7%)

PHY-1001 : Congestion index: top1 = 30.15, top5 = 22.75, top10 = 17.90, top15 = 13.89.
PHY-1001 : End incremental global routing;  0.189785s wall, 0.218750s user + 0.062500s system = 0.281250s CPU (148.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067919s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.288481s wall, 0.312500s user + 0.062500s system = 0.375000s CPU (130.0%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1688/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007287s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.15, top5 = 22.75, top10 = 17.90, top15 = 13.89.
OPT-1001 : End congestion update;  0.057629s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064197s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 746 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 784 instances, 737 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62471.2, Over = 0
PHY-3001 : End spreading;  0.006654s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62471.2, Over = 0
PHY-3001 : End incremental legalization;  0.042549s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (257.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.202250s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (131.3%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057438s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1684/1927.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.021385s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (365.3%)

PHY-1001 : Congestion index: top1 = 30.13, top5 = 22.73, top10 = 17.90, top15 = 13.89.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062323s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (100.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 29.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.991500s wall, 1.062500s user + 0.109375s system = 1.171875s CPU (118.2%)

RUN-1003 : finish command "place" in  6.597505s wall, 9.625000s user + 3.765625s system = 13.390625s CPU (203.0%)

RUN-1004 : used memory is 197 MB, reserved memory is 161 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 786 instances
RUN-1001 : 369 mslices, 368 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1927 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1385 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6251, tnet num: 1925, tinst num: 784, tnode num: 8426, tedge num: 11020.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 368 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68304, over cnt = 165(0%), over = 239, worst = 6
PHY-1002 : len = 69032, over cnt = 98(0%), over = 136, worst = 6
PHY-1002 : len = 70744, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 70792, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.180316s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.3%)

PHY-1001 : Congestion index: top1 = 29.78, top5 = 22.61, top10 = 17.86, top15 = 13.85.
PHY-1001 : End global routing;  0.231664s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 201, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 494, reserve = 462, peak = 494.
PHY-1001 : End build detailed router design. 3.356726s wall, 3.328125s user + 0.015625s system = 3.343750s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34592, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.364994s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 528, reserve = 496, peak = 528.
PHY-1001 : End phase 1; 1.371619s wall, 1.375000s user + 0.000000s system = 1.375000s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187904, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 529.
PHY-1001 : End initial routed; 1.508716s wall, 2.375000s user + 0.203125s system = 2.578125s CPU (170.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1701(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.356   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.190   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.370464s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 531.
PHY-1001 : End phase 2; 1.879269s wall, 2.750000s user + 0.203125s system = 2.953125s CPU (157.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187904, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.019462s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (80.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187776, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027639s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (56.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187848, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023734s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1701(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.356   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.190   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366207s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.170891s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 0.735348s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 187848
PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End export database. 0.011010s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.9%)

PHY-1001 : End detail routing;  7.539458s wall, 8.390625s user + 0.218750s system = 8.609375s CPU (114.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6251, tnet num: 1925, tinst num: 784, tnode num: 8426, tedge num: 11020.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[1] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6273, tnet num: 1936, tinst num: 795, tnode num: 8448, tedge num: 11042.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.066325s wall, 3.093750s user + 0.093750s system = 3.187500s CPU (104.0%)

RUN-1003 : finish command "route" in  11.215315s wall, 12.078125s user + 0.328125s system = 12.406250s CPU (110.6%)

RUN-1004 : used memory is 502 MB, reserved memory is 476 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      817   out of  19600    4.17%
#reg                      988   out of  19600    5.04%
#le                      1440
  #lut only               452   out of   1440   31.39%
  #reg only               623   out of   1440   43.26%
  #lut&reg                365   out of   1440   25.35%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         438
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1440   |605     |212     |1019    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1020   |281     |119     |834     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |444    |113     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |56     |36      |6       |48      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |15      |0       |16      |0       |0       |
|    integ                   |Integration                                      |132    |25      |14      |106     |0       |0       |
|    modu                    |Modulation                                       |94     |33      |21      |90      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |77      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |124    |114     |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |66     |66      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1360  
    #2          2       293   
    #3          3       122   
    #4          4        13   
    #5        5-10       78   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6273, tnet num: 1936, tinst num: 795, tnode num: 8448, tedge num: 11042.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1936 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 795
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1938, pip num: 14458
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1337 valid insts, and 38301 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.647288s wall, 19.687500s user + 0.046875s system = 19.734375s CPU (541.1%)

RUN-1004 : used memory is 520 MB, reserved memory is 490 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_171024.log"
