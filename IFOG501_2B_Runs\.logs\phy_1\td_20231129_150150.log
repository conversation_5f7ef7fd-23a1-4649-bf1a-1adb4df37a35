============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 15:01:50 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0111110101101110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3144/12 useful/useless nets, 1927/4 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1016 : Merged 12 instances.
SYN-1032 : 2901/16 useful/useless nets, 2250/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/reg0_syn_10
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 277 better
SYN-1014 : Optimize round 2
SYN-1032 : 2670/30 useful/useless nets, 2019/32 useful/useless insts
SYN-1015 : Optimize round 2, 77 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2694/157 useful/useless nets, 2066/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 13 instances.
SYN-2501 : Optimize round 1, 67 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3062/5 useful/useless nets, 2434/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11104, tnet num: 3062, tinst num: 2433, tnode num: 14846, tedge num: 17917.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3062 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 161 (3.66), #lev = 8 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 161 (3.67), #lev = 7 (1.99)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 371 instances into 161 LUTs, name keeping = 75%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 272 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.918039s wall, 1.875000s user + 0.031250s system = 1.906250s CPU (99.4%)

RUN-1004 : used memory is 167 MB, reserved memory is 127 MB, peak memory is 196 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (185 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2050 instances
RUN-0007 : 545 luts, 1143 seqs, 179 mslices, 110 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2680 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1865 nets have 2 pins
RUN-1001 : 642 nets have [3 - 5] pins
RUN-1001 : 104 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 38 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     235     
RUN-1001 :   No   |  No   |  Yes  |     212     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     276     
RUN-1001 :   Yes  |  No   |  Yes  |     310     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  16   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 26
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2048 instances, 545 luts, 1143 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10282, tnet num: 2678, tinst num: 2048, tnode num: 14223, tedge num: 17460.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2678 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.504058s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 694183
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2048.
PHY-3001 : End clustering;  0.000033s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 617904, overlap = 69.75
PHY-3002 : Step(2): len = 578600, overlap = 72
PHY-3002 : Step(3): len = 555575, overlap = 69.75
PHY-3002 : Step(4): len = 538956, overlap = 67.5
PHY-3002 : Step(5): len = 515041, overlap = 69.75
PHY-3002 : Step(6): len = 489339, overlap = 63
PHY-3002 : Step(7): len = 477590, overlap = 65.25
PHY-3002 : Step(8): len = 458535, overlap = 69.75
PHY-3002 : Step(9): len = 437834, overlap = 67.5
PHY-3002 : Step(10): len = 428500, overlap = 69.75
PHY-3002 : Step(11): len = 419876, overlap = 65.25
PHY-3002 : Step(12): len = 400447, overlap = 67.5
PHY-3002 : Step(13): len = 393401, overlap = 63
PHY-3002 : Step(14): len = 386965, overlap = 63
PHY-3002 : Step(15): len = 374589, overlap = 67.5
PHY-3002 : Step(16): len = 364734, overlap = 67.5
PHY-3002 : Step(17): len = 359664, overlap = 67.5
PHY-3002 : Step(18): len = 347918, overlap = 69.75
PHY-3002 : Step(19): len = 337835, overlap = 69.75
PHY-3002 : Step(20): len = 333469, overlap = 69.75
PHY-3002 : Step(21): len = 325879, overlap = 67.5
PHY-3002 : Step(22): len = 299277, overlap = 67.5
PHY-3002 : Step(23): len = 290345, overlap = 67.5
PHY-3002 : Step(24): len = 286988, overlap = 67.5
PHY-3002 : Step(25): len = 247279, overlap = 69.75
PHY-3002 : Step(26): len = 227054, overlap = 69.75
PHY-3002 : Step(27): len = 225136, overlap = 69.75
PHY-3002 : Step(28): len = 217721, overlap = 67.5
PHY-3002 : Step(29): len = 202438, overlap = 69.75
PHY-3002 : Step(30): len = 197437, overlap = 72
PHY-3002 : Step(31): len = 193984, overlap = 72
PHY-3002 : Step(32): len = 190591, overlap = 72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.00101e-05
PHY-3002 : Step(33): len = 193586, overlap = 72
PHY-3002 : Step(34): len = 189506, overlap = 63
PHY-3002 : Step(35): len = 188493, overlap = 60.75
PHY-3002 : Step(36): len = 188522, overlap = 60.75
PHY-3002 : Step(37): len = 184396, overlap = 60.75
PHY-3002 : Step(38): len = 183291, overlap = 60.75
PHY-3002 : Step(39): len = 182512, overlap = 63
PHY-3002 : Step(40): len = 181034, overlap = 63
PHY-3002 : Step(41): len = 179949, overlap = 58.5
PHY-3002 : Step(42): len = 179744, overlap = 58.5
PHY-3002 : Step(43): len = 178700, overlap = 56.25
PHY-3002 : Step(44): len = 174000, overlap = 56.25
PHY-3002 : Step(45): len = 171412, overlap = 56.25
PHY-3002 : Step(46): len = 169574, overlap = 56.25
PHY-3002 : Step(47): len = 167064, overlap = 58.5
PHY-3002 : Step(48): len = 153521, overlap = 56.4375
PHY-3002 : Step(49): len = 148758, overlap = 58.875
PHY-3002 : Step(50): len = 147801, overlap = 56.5625
PHY-3002 : Step(51): len = 146592, overlap = 54.25
PHY-3002 : Step(52): len = 145947, overlap = 54.25
PHY-3002 : Step(53): len = 145410, overlap = 56.5
PHY-3002 : Step(54): len = 144296, overlap = 56.5
PHY-3002 : Step(55): len = 141344, overlap = 58.6875
PHY-3002 : Step(56): len = 138107, overlap = 58.8125
PHY-3002 : Step(57): len = 136800, overlap = 56.5625
PHY-3002 : Step(58): len = 135450, overlap = 54.125
PHY-3002 : Step(59): len = 133090, overlap = 54.125
PHY-3002 : Step(60): len = 129141, overlap = 56.375
PHY-3002 : Step(61): len = 125496, overlap = 56.25
PHY-3002 : Step(62): len = 123862, overlap = 56.25
PHY-3002 : Step(63): len = 123220, overlap = 56.25
PHY-3002 : Step(64): len = 122788, overlap = 56.25
PHY-3002 : Step(65): len = 121528, overlap = 56.25
PHY-3002 : Step(66): len = 111621, overlap = 56.25
PHY-3002 : Step(67): len = 108720, overlap = 51.75
PHY-3002 : Step(68): len = 108252, overlap = 56.25
PHY-3002 : Step(69): len = 107715, overlap = 56.25
PHY-3002 : Step(70): len = 106960, overlap = 56.25
PHY-3002 : Step(71): len = 106171, overlap = 54
PHY-3002 : Step(72): len = 104637, overlap = 56.25
PHY-3002 : Step(73): len = 103337, overlap = 56.25
PHY-3002 : Step(74): len = 101989, overlap = 51.75
PHY-3002 : Step(75): len = 99920.8, overlap = 54
PHY-3002 : Step(76): len = 96706.2, overlap = 56.25
PHY-3002 : Step(77): len = 95098.1, overlap = 54
PHY-3002 : Step(78): len = 94235.2, overlap = 51.75
PHY-3002 : Step(79): len = 93506.2, overlap = 54
PHY-3002 : Step(80): len = 92298.8, overlap = 56.25
PHY-3002 : Step(81): len = 91839.7, overlap = 54
PHY-3002 : Step(82): len = 91009.3, overlap = 51.75
PHY-3002 : Step(83): len = 89856.3, overlap = 54
PHY-3002 : Step(84): len = 88956.1, overlap = 51.8125
PHY-3002 : Step(85): len = 88292, overlap = 52
PHY-3002 : Step(86): len = 86929.4, overlap = 52.5
PHY-3002 : Step(87): len = 85512.8, overlap = 52.6875
PHY-3002 : Step(88): len = 84600.8, overlap = 52.5625
PHY-3002 : Step(89): len = 84005, overlap = 52.5625
PHY-3002 : Step(90): len = 82796.1, overlap = 54.75
PHY-3002 : Step(91): len = 82167.5, overlap = 52.5
PHY-3002 : Step(92): len = 81775.7, overlap = 52.5
PHY-3002 : Step(93): len = 81710.1, overlap = 52.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00016002
PHY-3002 : Step(94): len = 81722, overlap = 52.5
PHY-3002 : Step(95): len = 81736.5, overlap = 52.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00032004
PHY-3002 : Step(96): len = 81817, overlap = 52.5
PHY-3002 : Step(97): len = 81855.7, overlap = 52.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.019574s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (159.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2678 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.141320s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (88.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(98): len = 90616.5, overlap = 13.4688
PHY-3002 : Step(99): len = 90627.4, overlap = 13.5312
PHY-3002 : Step(100): len = 89006.5, overlap = 13.4062
PHY-3002 : Step(101): len = 88786.1, overlap = 12.0938
PHY-3002 : Step(102): len = 88403.5, overlap = 11.6875
PHY-3002 : Step(103): len = 86841.6, overlap = 13.2188
PHY-3002 : Step(104): len = 85418.7, overlap = 19.5
PHY-3002 : Step(105): len = 82578.3, overlap = 16.25
PHY-3002 : Step(106): len = 81305.4, overlap = 16.125
PHY-3002 : Step(107): len = 79534.7, overlap = 17.375
PHY-3002 : Step(108): len = 78236.7, overlap = 19.2812
PHY-3002 : Step(109): len = 76390, overlap = 22.125
PHY-3002 : Step(110): len = 74404.5, overlap = 23.9062
PHY-3002 : Step(111): len = 73175.2, overlap = 24
PHY-3002 : Step(112): len = 72666.5, overlap = 23.25
PHY-3002 : Step(113): len = 70734, overlap = 28.7188
PHY-3002 : Step(114): len = 69970.3, overlap = 30.6875
PHY-3002 : Step(115): len = 69226.5, overlap = 31.3438
PHY-3002 : Step(116): len = 68843.6, overlap = 31.9688
PHY-3002 : Step(117): len = 67932.6, overlap = 35.7188
PHY-3002 : Step(118): len = 67100.5, overlap = 35.9062
PHY-3002 : Step(119): len = 66564.2, overlap = 35.2812
PHY-3002 : Step(120): len = 66307.6, overlap = 34.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0119429
PHY-3002 : Step(121): len = 66245.3, overlap = 33.9062
PHY-3002 : Step(122): len = 66087.2, overlap = 33.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2678 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.110200s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.3389e-05
PHY-3002 : Step(123): len = 67195.7, overlap = 81.7188
PHY-3002 : Step(124): len = 68831.2, overlap = 73.9688
PHY-3002 : Step(125): len = 68680.5, overlap = 68.6562
PHY-3002 : Step(126): len = 68038.2, overlap = 66.9688
PHY-3002 : Step(127): len = 67810.2, overlap = 62.9062
PHY-3002 : Step(128): len = 67793.9, overlap = 58.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.6778e-05
PHY-3002 : Step(129): len = 68213.4, overlap = 57.125
PHY-3002 : Step(130): len = 69014.9, overlap = 56.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000123204
PHY-3002 : Step(131): len = 68891.5, overlap = 52.25
PHY-3002 : Step(132): len = 70074.5, overlap = 51.5625
PHY-3002 : Step(133): len = 71345.4, overlap = 49.375
PHY-3002 : Step(134): len = 71810.5, overlap = 49.3438
PHY-3002 : Step(135): len = 72150.5, overlap = 48.0938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000246408
PHY-3002 : Step(136): len = 72213.3, overlap = 47.1562
PHY-3002 : Step(137): len = 72827.4, overlap = 44.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10282, tnet num: 2678, tinst num: 2048, tnode num: 14223, tedge num: 17460.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 121.69 peak overflow 3.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2680.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 81104, over cnt = 330(0%), over = 1447, worst = 28
PHY-1001 : End global iterations;  0.171748s wall, 0.203125s user + 0.078125s system = 0.281250s CPU (163.8%)

PHY-1001 : Congestion index: top1 = 50.34, top5 = 29.15, top10 = 20.58, top15 = 15.48.
PHY-1001 : End incremental global routing;  0.253765s wall, 0.281250s user + 0.078125s system = 0.359375s CPU (141.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2678 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.129016s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.436184s wall, 0.468750s user + 0.078125s system = 0.546875s CPU (125.4%)

OPT-1001 : Current memory(MB): used = 230, reserve = 188, peak = 230.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2007/2680.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 81104, over cnt = 330(0%), over = 1447, worst = 28
PHY-1002 : len = 91376, over cnt = 237(0%), over = 673, worst = 16
PHY-1002 : len = 97128, over cnt = 68(0%), over = 147, worst = 14
PHY-1002 : len = 99272, over cnt = 14(0%), over = 22, worst = 3
PHY-1002 : len = 99536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.229907s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (122.3%)

PHY-1001 : Congestion index: top1 = 43.06, top5 = 29.24, top10 = 22.19, top15 = 17.62.
OPT-1001 : End congestion update;  0.299314s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (109.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2678 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.103666s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (105.5%)

OPT-0007 : Start: WNS -3047 TNS -55174 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -3047 TNS -55174 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.407359s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (107.4%)

OPT-1001 : Current memory(MB): used = 234, reserve = 192, peak = 234.
OPT-1001 : End physical optimization;  1.285069s wall, 1.453125s user + 0.078125s system = 1.531250s CPU (119.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 545 LUT to BLE ...
SYN-4008 : Packed 545 LUT and 231 SEQ to BLE.
SYN-4003 : Packing 912 remaining SEQ's ...
SYN-4005 : Packed 186 SEQ with LUT/SLICE
SYN-4006 : 162 single LUT's are left
SYN-4006 : 726 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1271/1667 primitive instances ...
PHY-3001 : End packing;  0.087312s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1035 instances
RUN-1001 : 481 mslices, 481 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2458 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 645 nets have [3 - 5] pins
RUN-1001 : 105 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 36 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 1033 instances, 962 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 73248.4, Over = 73.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8759, tnet num: 2456, tinst num: 1033, tnode num: 11602, tedge num: 15389.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.485323s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.23306e-05
PHY-3002 : Step(138): len = 72413.5, overlap = 70.75
PHY-3002 : Step(139): len = 71819.4, overlap = 68.75
PHY-3002 : Step(140): len = 71686.4, overlap = 67.25
PHY-3002 : Step(141): len = 71174, overlap = 66.5
PHY-3002 : Step(142): len = 71189, overlap = 64.75
PHY-3002 : Step(143): len = 70960.2, overlap = 68
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.46612e-05
PHY-3002 : Step(144): len = 70938.7, overlap = 67.5
PHY-3002 : Step(145): len = 71403.5, overlap = 67.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.74618e-05
PHY-3002 : Step(146): len = 72470.3, overlap = 64.25
PHY-3002 : Step(147): len = 73512.1, overlap = 62
PHY-3002 : Step(148): len = 74348.8, overlap = 59
PHY-3002 : Step(149): len = 74600.5, overlap = 57
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.251854s wall, 0.109375s user + 0.234375s system = 0.343750s CPU (136.5%)

PHY-3001 : Trial Legalized: Len = 93133.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.115785s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00116576
PHY-3002 : Step(150): len = 88509.8, overlap = 5.25
PHY-3002 : Step(151): len = 86743.6, overlap = 8.25
PHY-3002 : Step(152): len = 83629.7, overlap = 17
PHY-3002 : Step(153): len = 81352.7, overlap = 19.5
PHY-3002 : Step(154): len = 80198.9, overlap = 21
PHY-3002 : Step(155): len = 79490.9, overlap = 24.75
PHY-3002 : Step(156): len = 78936.5, overlap = 27.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00233153
PHY-3002 : Step(157): len = 79105.8, overlap = 26.75
PHY-3002 : Step(158): len = 79149.9, overlap = 26.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00466305
PHY-3002 : Step(159): len = 79194.7, overlap = 26.25
PHY-3002 : Step(160): len = 79189.5, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008300s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 84939, Over = 0
PHY-3001 : Spreading special nets. 30 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.013465s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 43 instances has been re-located, deltaX = 26, deltaY = 28, maxDist = 3.
PHY-3001 : Final: Len = 86037, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8759, tnet num: 2456, tinst num: 1033, tnode num: 11602, tedge num: 15389.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 62/2458.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 99472, over cnt = 250(0%), over = 452, worst = 7
PHY-1002 : len = 101888, over cnt = 152(0%), over = 204, worst = 5
PHY-1002 : len = 103832, over cnt = 36(0%), over = 58, worst = 5
PHY-1002 : len = 104552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.283309s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (132.4%)

PHY-1001 : Congestion index: top1 = 34.46, top5 = 26.66, top10 = 21.49, top15 = 17.85.
PHY-1001 : End incremental global routing;  0.355932s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (127.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.105313s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.517627s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (117.7%)

OPT-1001 : Current memory(MB): used = 239, reserve = 196, peak = 239.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2132/2458.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 104552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.019110s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.8%)

PHY-1001 : Congestion index: top1 = 34.46, top5 = 26.66, top10 = 21.49, top15 = 17.85.
OPT-1001 : End congestion update;  0.096867s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (112.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079090s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.8%)

OPT-0007 : Start: WNS -3047 TNS -54874 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -3047 TNS -54874 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.179608s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.4%)

OPT-1001 : Current memory(MB): used = 241, reserve = 197, peak = 241.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084469s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2132/2458.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 104552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009462s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (165.1%)

PHY-1001 : Congestion index: top1 = 34.46, top5 = 26.66, top10 = 21.49, top15 = 17.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.094829s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -3047 TNS -54874 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 34.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -3047ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2458 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2458 nets
OPT-1001 : End physical optimization;  1.419457s wall, 1.484375s user + 0.031250s system = 1.515625s CPU (106.8%)

RUN-1003 : finish command "place" in  10.916372s wall, 15.703125s user + 5.500000s system = 21.203125s CPU (194.2%)

RUN-1004 : used memory is 227 MB, reserved memory is 184 MB, peak memory is 241 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1035 instances
RUN-1001 : 481 mslices, 481 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2458 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 645 nets have [3 - 5] pins
RUN-1001 : 105 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 36 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8759, tnet num: 2456, tinst num: 1033, tnode num: 11602, tedge num: 15389.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 481 mslices, 481 lslices, 34 pads, 28 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98416, over cnt = 252(0%), over = 463, worst = 7
PHY-1002 : len = 101048, over cnt = 156(0%), over = 213, worst = 4
PHY-1002 : len = 103024, over cnt = 44(0%), over = 53, worst = 4
PHY-1002 : len = 103752, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 103784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.301611s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (129.5%)

PHY-1001 : Congestion index: top1 = 34.25, top5 = 26.28, top10 = 21.22, top15 = 17.68.
PHY-1001 : End global routing;  0.370519s wall, 0.375000s user + 0.062500s system = 0.437500s CPU (118.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 258, reserve = 215, peak = 268.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 519, reserve = 481, peak = 519.
PHY-1001 : End build detailed router design. 4.268013s wall, 4.203125s user + 0.046875s system = 4.250000s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35992, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.324731s wall, 2.328125s user + 0.000000s system = 2.328125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 551, reserve = 514, peak = 551.
PHY-1001 : End phase 1; 2.331428s wall, 2.328125s user + 0.000000s system = 2.328125s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 307296, over cnt = 78(0%), over = 78, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 552, reserve = 515, peak = 553.
PHY-1001 : End initial routed; 4.166143s wall, 5.343750s user + 0.203125s system = 5.546875s CPU (133.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2168(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.246   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.010   |  -6.033   |  16   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.602289s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 553, reserve = 516, peak = 553.
PHY-1001 : End phase 2; 4.768703s wall, 5.953125s user + 0.203125s system = 6.156250s CPU (129.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 307296, over cnt = 78(0%), over = 78, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.024606s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 306768, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.080752s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 306888, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.045800s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 306728, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.037583s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.1%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 306768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.061840s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2168(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.246   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.010   |  -6.033   |  16   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.555455s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (101.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.356980s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 570, reserve = 533, peak = 570.
PHY-1001 : End phase 3; 1.340961s wall, 1.343750s user + 0.000000s system = 1.343750s CPU (100.2%)

PHY-1003 : Routed, final wirelength = 306768
PHY-1001 : Current memory(MB): used = 571, reserve = 534, peak = 571.
PHY-1001 : End export database. 0.012799s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  12.958795s wall, 14.078125s user + 0.250000s system = 14.328125s CPU (110.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8759, tnet num: 2456, tinst num: 1033, tnode num: 11602, tedge num: 15389.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  14.419301s wall, 15.546875s user + 0.312500s system = 15.859375s CPU (110.0%)

RUN-1004 : used memory is 546 MB, reserved memory is 509 MB, peak memory is 571 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1130   out of  19600    5.77%
#reg                     1198   out of  19600    6.11%
#le                      1856
  #lut only               658   out of   1856   35.45%
  #reg only               726   out of   1856   39.12%
  #lut&reg                472   out of   1856   25.43%
#dsp                        4   out of     29   13.79%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     5
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       393
#2        config_inst_syn_9               GCLK               config             config_inst.jtck            102
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       100
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di             73
#5        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    45
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1856   |841     |289     |1216    |28      |4       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |980    |262     |105     |774     |4       |4       |
|    ctrl_signal                     |SignalGenerator                                  |36     |30      |6       |22      |0       |0       |
|    demodu                          |Demodulation                                     |429    |83      |44      |345     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |53     |28      |6       |45      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |20     |10      |0       |20      |0       |0       |
|    integ                           |Integration                                      |140    |17      |14      |113     |0       |0       |
|    modu                            |Modulation                                       |28     |15      |7       |26      |0       |0       |
|    rs422                           |Rs422Output                                      |316    |91      |29      |250     |0       |4       |
|    trans                           |SquareWaveGenerator                              |31     |26      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |138    |120     |7       |55      |0       |0       |
|    U0                              |speed_select_Tx                                  |34     |27      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |24     |19      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |80     |74      |0       |23      |0       |0       |
|  wendu                             |DS18B20                                          |209    |164     |45      |73      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |444    |251     |91      |270     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |444    |251     |91      |270     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |176    |90      |0       |176     |0       |0       |
|        reg_inst                    |register                                         |173    |87      |0       |173     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |268    |161     |91      |94      |0       |0       |
|        bus_inst                    |bus_top                                          |73     |34      |30      |17      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |33     |16      |16      |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |40     |18      |14      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |110    |81      |29      |55      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1604  
    #2          2       435   
    #3          3       196   
    #4          4        14   
    #5        5-10      111   
    #6        11-50      51   
    #7       51-100      1    
    #8       101-500     2    
  Average     2.30            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8759, tnet num: 2456, tinst num: 1033, tnode num: 11602, tedge num: 15389.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2456 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1033
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2458, pip num: 21292
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1665 valid insts, and 55752 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000111110101101110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.495869s wall, 33.437500s user + 0.171875s system = 33.609375s CPU (517.4%)

RUN-1004 : used memory is 571 MB, reserved memory is 533 MB, peak memory is 694 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_150150.log"
