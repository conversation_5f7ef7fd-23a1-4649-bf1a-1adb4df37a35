=========================================================================================================
Auto created by Tang Dynasty v5.6.71036
   Copyright (c) 2012-2023 Anlogic Inc.
Thu Jul 27 20:44:53 2023
=========================================================================================================


Top Model:                IFOG501_2B                                                      
Device:                   eagle_s20                                                       
Timing Constraint File:   ../../Constraints/IFOG_11FB.sdc                                 
STA Level:                Detail                                                          
Speed Grade:              NA                                                              

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              7     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              7     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

---------------------------------------------------------------------------------------------------------

=========================================================================================================
 Timing constraint:       false path info                                                 
False path: set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]

7 endpoints analyzed totally, and 7 paths analyzed
---------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.800ns  (logic 0.239ns, net 0.561ns, 29% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.q[1] clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[1] net  (fanout = 1)       0.561 r     0.689
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30  path2reg1               0.111       0.800
 Arrival time                                                                        0.800                  (0 lvl)       

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.786ns  (logic 0.239ns, net 0.547ns, 30% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk        clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/ram_inst/reg2_syn_173.q[0]       clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[0] net  (fanout = 1)       0.547 r     0.675
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30  path2reg0               0.111       0.786
 Arrival time                                                                        0.786                  (0 lvl)       

Paths for end point signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.700ns  (logic 0.239ns, net 0.461ns, 34% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.q[1] clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27.mi[1] net  (fanout = 1)       0.461 r     0.589
 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27  path2reg1               0.111       0.700
 Arrival time                                                                        0.700                  (0 lvl)       


=========================================================================================================
 Timing constraint:       false path info                                                 
False path: set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

7 endpoints analyzed totally, and 7 paths analyzed
---------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------
Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.800ns  (logic 0.239ns, net 0.561ns, 29% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.q[1] clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[1] net  (fanout = 1)       0.561 r     0.689
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31  path2reg1               0.111       0.800
 Arrival time                                                                        0.800                  (0 lvl)       

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_42.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.654ns  (logic 0.239ns, net 0.415ns, 36% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_42.clk clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_42.q[0] clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0] net  (fanout = 1)       0.415 r     0.543
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31  path2reg0               0.111       0.654
 Arrival time                                                                        0.654                  (0 lvl)       

Paths for end point signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.000 ns                                                        
 Start Point:             signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk (rising edge triggered by clock CLK120/pll_inst.clkc[3])
 End Point:               signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         0.879ns  (logic 0.239ns, net 0.640ns, 27% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path
---------------------------------------------------------------------------------------------------------
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk clock                   0.000       0.000
 launch clock edge                                                                   0.000       0.000
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.q[0] clk2q                   0.128 r     0.128
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[1] net  (fanout = 1)       0.640 r     0.768
 signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28  path2reg1               0.111       0.879
 Arrival time                                                                        0.879                  (0 lvl)       


---------------------------------------------------------------------------------------------------------
