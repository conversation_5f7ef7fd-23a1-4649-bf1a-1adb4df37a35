============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Aug 21 15:12:31 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1650 instances
RUN-0007 : 395 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2220 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1661 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1648 instances, 395 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7886, tnet num: 2218, tinst num: 1648, tnode num: 11126, tedge num: 13321.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297999s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 598950
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1648.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 478199, overlap = 20.25
PHY-3002 : Step(2): len = 441768, overlap = 20.25
PHY-3002 : Step(3): len = 393898, overlap = 20.25
PHY-3002 : Step(4): len = 369112, overlap = 15.75
PHY-3002 : Step(5): len = 359020, overlap = 18
PHY-3002 : Step(6): len = 348310, overlap = 18
PHY-3002 : Step(7): len = 338258, overlap = 18
PHY-3002 : Step(8): len = 315667, overlap = 20.25
PHY-3002 : Step(9): len = 306809, overlap = 20.25
PHY-3002 : Step(10): len = 302564, overlap = 20.25
PHY-3002 : Step(11): len = 293345, overlap = 20.25
PHY-3002 : Step(12): len = 284607, overlap = 20.25
PHY-3002 : Step(13): len = 278706, overlap = 20.25
PHY-3002 : Step(14): len = 273189, overlap = 20.25
PHY-3002 : Step(15): len = 265466, overlap = 20.25
PHY-3002 : Step(16): len = 259688, overlap = 20.25
PHY-3002 : Step(17): len = 254057, overlap = 20.25
PHY-3002 : Step(18): len = 248041, overlap = 20.25
PHY-3002 : Step(19): len = 242152, overlap = 20.25
PHY-3002 : Step(20): len = 238127, overlap = 20.25
PHY-3002 : Step(21): len = 230880, overlap = 20.25
PHY-3002 : Step(22): len = 226946, overlap = 20.25
PHY-3002 : Step(23): len = 222143, overlap = 20.25
PHY-3002 : Step(24): len = 217721, overlap = 20.25
PHY-3002 : Step(25): len = 211804, overlap = 20.25
PHY-3002 : Step(26): len = 207883, overlap = 20.25
PHY-3002 : Step(27): len = 203641, overlap = 20.25
PHY-3002 : Step(28): len = 199933, overlap = 20.25
PHY-3002 : Step(29): len = 193148, overlap = 20.25
PHY-3002 : Step(30): len = 190052, overlap = 20.25
PHY-3002 : Step(31): len = 186064, overlap = 20.25
PHY-3002 : Step(32): len = 179132, overlap = 20.25
PHY-3002 : Step(33): len = 172929, overlap = 20.25
PHY-3002 : Step(34): len = 171365, overlap = 20.25
PHY-3002 : Step(35): len = 142945, overlap = 18
PHY-3002 : Step(36): len = 131918, overlap = 20.25
PHY-3002 : Step(37): len = 130235, overlap = 20.25
PHY-3002 : Step(38): len = 126020, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000119923
PHY-3002 : Step(39): len = 128320, overlap = 13.5
PHY-3002 : Step(40): len = 128398, overlap = 13.5
PHY-3002 : Step(41): len = 126319, overlap = 13.5
PHY-3002 : Step(42): len = 123574, overlap = 18
PHY-3002 : Step(43): len = 121665, overlap = 15.75
PHY-3002 : Step(44): len = 119949, overlap = 11.25
PHY-3002 : Step(45): len = 116759, overlap = 11.25
PHY-3002 : Step(46): len = 114217, overlap = 13.5
PHY-3002 : Step(47): len = 111855, overlap = 9
PHY-3002 : Step(48): len = 109990, overlap = 11.25
PHY-3002 : Step(49): len = 105555, overlap = 9
PHY-3002 : Step(50): len = 104305, overlap = 15.75
PHY-3002 : Step(51): len = 102282, overlap = 11.25
PHY-3002 : Step(52): len = 98935.3, overlap = 9
PHY-3002 : Step(53): len = 95531.9, overlap = 11.25
PHY-3002 : Step(54): len = 94759.6, overlap = 11.25
PHY-3002 : Step(55): len = 92899.4, overlap = 13.5
PHY-3002 : Step(56): len = 91429.5, overlap = 13.5
PHY-3002 : Step(57): len = 88652.4, overlap = 6.75
PHY-3002 : Step(58): len = 87561.8, overlap = 9
PHY-3002 : Step(59): len = 85822.1, overlap = 13.5
PHY-3002 : Step(60): len = 84950.9, overlap = 13.5
PHY-3002 : Step(61): len = 84163.4, overlap = 11.25
PHY-3002 : Step(62): len = 83733.1, overlap = 11.25
PHY-3002 : Step(63): len = 81721.7, overlap = 10.125
PHY-3002 : Step(64): len = 79267.8, overlap = 12
PHY-3002 : Step(65): len = 78820.7, overlap = 14.1875
PHY-3002 : Step(66): len = 77764.8, overlap = 14.4375
PHY-3002 : Step(67): len = 76305.9, overlap = 14.1875
PHY-3002 : Step(68): len = 74262.4, overlap = 12.375
PHY-3002 : Step(69): len = 72745.2, overlap = 14.4375
PHY-3002 : Step(70): len = 71188.6, overlap = 14.3125
PHY-3002 : Step(71): len = 70891.2, overlap = 10.0625
PHY-3002 : Step(72): len = 70123, overlap = 12.4375
PHY-3002 : Step(73): len = 69042.5, overlap = 12.25
PHY-3002 : Step(74): len = 68499.4, overlap = 12.1875
PHY-3002 : Step(75): len = 67368.9, overlap = 14.75
PHY-3002 : Step(76): len = 66531.9, overlap = 12.1875
PHY-3002 : Step(77): len = 65289.5, overlap = 10.25
PHY-3002 : Step(78): len = 64007.5, overlap = 13.0938
PHY-3002 : Step(79): len = 62609.1, overlap = 13.2812
PHY-3002 : Step(80): len = 62211.2, overlap = 13.625
PHY-3002 : Step(81): len = 61613.9, overlap = 16.0312
PHY-3002 : Step(82): len = 61084.8, overlap = 14.125
PHY-3002 : Step(83): len = 60901.6, overlap = 14.1875
PHY-3002 : Step(84): len = 60764.6, overlap = 14.6562
PHY-3002 : Step(85): len = 60392.5, overlap = 14.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000239846
PHY-3002 : Step(86): len = 60433.1, overlap = 12.5312
PHY-3002 : Step(87): len = 60441.2, overlap = 12.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000479692
PHY-3002 : Step(88): len = 60319.4, overlap = 12.4688
PHY-3002 : Step(89): len = 60268.6, overlap = 12.4688
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006391s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068946s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00319276
PHY-3002 : Step(90): len = 63327.7, overlap = 15.7812
PHY-3002 : Step(91): len = 62191.6, overlap = 15.0938
PHY-3002 : Step(92): len = 61249.2, overlap = 15.6562
PHY-3002 : Step(93): len = 60678.6, overlap = 15.4375
PHY-3002 : Step(94): len = 59793, overlap = 14.5
PHY-3002 : Step(95): len = 58239.3, overlap = 13.625
PHY-3002 : Step(96): len = 57148.6, overlap = 12.9688
PHY-3002 : Step(97): len = 55882.5, overlap = 12.75
PHY-3002 : Step(98): len = 55322.5, overlap = 13.125
PHY-3002 : Step(99): len = 54915.7, overlap = 12.9375
PHY-3002 : Step(100): len = 54416.1, overlap = 11.8125
PHY-3002 : Step(101): len = 53799.1, overlap = 12.625
PHY-3002 : Step(102): len = 52975.7, overlap = 12.25
PHY-3002 : Step(103): len = 52054.7, overlap = 10.1562
PHY-3002 : Step(104): len = 51010.1, overlap = 9.46875
PHY-3002 : Step(105): len = 50627.7, overlap = 10.3125
PHY-3002 : Step(106): len = 50386.3, overlap = 11.8438
PHY-3002 : Step(107): len = 50039.9, overlap = 13.5
PHY-3002 : Step(108): len = 49579, overlap = 13.4062
PHY-3002 : Step(109): len = 48778.8, overlap = 13.1875
PHY-3002 : Step(110): len = 48340.7, overlap = 14.3438
PHY-3002 : Step(111): len = 47937.2, overlap = 15.4375
PHY-3002 : Step(112): len = 47410.1, overlap = 17.5938
PHY-3002 : Step(113): len = 47207.5, overlap = 19.125
PHY-3002 : Step(114): len = 46637.5, overlap = 19.0625
PHY-3002 : Step(115): len = 46526.8, overlap = 18.9375
PHY-3002 : Step(116): len = 46462.5, overlap = 18.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00638552
PHY-3002 : Step(117): len = 46272.7, overlap = 18.9062
PHY-3002 : Step(118): len = 46272.7, overlap = 18.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.012771
PHY-3002 : Step(119): len = 46224, overlap = 18.9688
PHY-3002 : Step(120): len = 46224, overlap = 18.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066224s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.5462e-05
PHY-3002 : Step(121): len = 47133.5, overlap = 78.5938
PHY-3002 : Step(122): len = 48028.3, overlap = 76.5938
PHY-3002 : Step(123): len = 47793, overlap = 74.0312
PHY-3002 : Step(124): len = 47789.3, overlap = 74.3438
PHY-3002 : Step(125): len = 47991.6, overlap = 74.5625
PHY-3002 : Step(126): len = 47720.6, overlap = 74.5938
PHY-3002 : Step(127): len = 47949.4, overlap = 73.3438
PHY-3002 : Step(128): len = 48073.1, overlap = 69.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000150924
PHY-3002 : Step(129): len = 47863.7, overlap = 68.1562
PHY-3002 : Step(130): len = 48140.4, overlap = 65.4688
PHY-3002 : Step(131): len = 49534, overlap = 61
PHY-3002 : Step(132): len = 50470.3, overlap = 57.7812
PHY-3002 : Step(133): len = 50347.3, overlap = 57.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000301848
PHY-3002 : Step(134): len = 50425.3, overlap = 56.25
PHY-3002 : Step(135): len = 51144.2, overlap = 45.25
PHY-3002 : Step(136): len = 51449.3, overlap = 44.75
PHY-3002 : Step(137): len = 51522.4, overlap = 44.375
PHY-3002 : Step(138): len = 51663.5, overlap = 43.8125
PHY-3002 : Step(139): len = 51777.5, overlap = 43.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000603696
PHY-3002 : Step(140): len = 51958.5, overlap = 39.6875
PHY-3002 : Step(141): len = 52232.2, overlap = 39.25
PHY-3002 : Step(142): len = 52792.7, overlap = 33.3438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00120739
PHY-3002 : Step(143): len = 52879.6, overlap = 32.9688
PHY-3002 : Step(144): len = 53202.7, overlap = 31.3125
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00241478
PHY-3002 : Step(145): len = 53565.1, overlap = 30.375
PHY-3002 : Step(146): len = 53600.7, overlap = 29.875
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00482957
PHY-3002 : Step(147): len = 53680, overlap = 29.4688
PHY-3002 : Step(148): len = 53680, overlap = 29.4688
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00965913
PHY-3002 : Step(149): len = 53835.6, overlap = 29.1875
PHY-3002 : Step(150): len = 53835.6, overlap = 29.1875
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0193183
PHY-3002 : Step(151): len = 53866.1, overlap = 29.2812
PHY-3002 : Step(152): len = 53854.9, overlap = 29.1562
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0352914
PHY-3002 : Step(153): len = 53892.7, overlap = 29.1562
PHY-3002 : Step(154): len = 53892.7, overlap = 29.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7886, tnet num: 2218, tinst num: 1648, tnode num: 11126, tedge num: 13321.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.84 peak overflow 1.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2220.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58776, over cnt = 281(0%), over = 1014, worst = 15
PHY-1001 : End global iterations;  0.063255s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (321.1%)

PHY-1001 : Congestion index: top1 = 40.95, top5 = 25.61, top10 = 17.14, top15 = 12.28.
PHY-1001 : End incremental global routing;  0.118763s wall, 0.171875s user + 0.078125s system = 0.250000s CPU (210.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074536s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (104.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1609 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1653 instances, 395 luts, 990 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 53999.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7906, tnet num: 2223, tinst num: 1653, tnode num: 11161, tedge num: 13351.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2223 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.333125s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(155): len = 54099.9, overlap = 2.25
PHY-3002 : Step(156): len = 54221.1, overlap = 2.3125
PHY-3002 : Step(157): len = 54247.2, overlap = 2.3125
PHY-3002 : Step(158): len = 54214.8, overlap = 2.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2223 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064056s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000463305
PHY-3002 : Step(159): len = 54218.3, overlap = 29.1562
PHY-3002 : Step(160): len = 54218.3, overlap = 29.1562
PHY-3001 : Final: Len = 54218.3, Over = 29.1562
PHY-3001 : End incremental placement;  0.491131s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (120.9%)

OPT-1001 : Total overflow 80.91 peak overflow 1.94
OPT-1001 : End high-fanout net optimization;  0.723869s wall, 0.812500s user + 0.140625s system = 0.953125s CPU (131.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 187, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1719/2225.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59016, over cnt = 282(0%), over = 1011, worst = 15
PHY-1002 : len = 64952, over cnt = 161(0%), over = 325, worst = 11
PHY-1002 : len = 67680, over cnt = 27(0%), over = 42, worst = 7
PHY-1002 : len = 68104, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 68296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096420s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (129.6%)

PHY-1001 : Congestion index: top1 = 34.72, top5 = 25.04, top10 = 18.27, top15 = 13.65.
OPT-1001 : End congestion update;  0.142958s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (120.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2223 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061960s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.207847s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (112.8%)

OPT-1001 : Current memory(MB): used = 219, reserve = 185, peak = 221.
OPT-1001 : End physical optimization;  1.204993s wall, 1.421875s user + 0.140625s system = 1.562500s CPU (129.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 808 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 121 single LUT's are left
SYN-4006 : 695 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1090/1418 primitive instances ...
PHY-3001 : End packing;  0.052997s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2058 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1501 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 84 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 54234, Over = 54.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6687, tnet num: 2056, tinst num: 844, tnode num: 9052, tedge num: 11728.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.315283s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.08879e-05
PHY-3002 : Step(161): len = 53522.1, overlap = 53.25
PHY-3002 : Step(162): len = 53231.6, overlap = 54.25
PHY-3002 : Step(163): len = 52656, overlap = 56
PHY-3002 : Step(164): len = 52180.2, overlap = 56.75
PHY-3002 : Step(165): len = 52105.4, overlap = 57.5
PHY-3002 : Step(166): len = 51898, overlap = 61.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.17758e-05
PHY-3002 : Step(167): len = 52375.1, overlap = 56.75
PHY-3002 : Step(168): len = 52760.3, overlap = 56.5
PHY-3002 : Step(169): len = 53030.9, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000123552
PHY-3002 : Step(170): len = 54064.6, overlap = 52.5
PHY-3002 : Step(171): len = 55476.3, overlap = 49.25
PHY-3002 : Step(172): len = 55479.1, overlap = 46.75
PHY-3002 : Step(173): len = 55491, overlap = 47.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086909s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (179.8%)

PHY-3001 : Trial Legalized: Len = 68746.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057081s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000624124
PHY-3002 : Step(174): len = 66181.8, overlap = 6
PHY-3002 : Step(175): len = 63991, overlap = 12
PHY-3002 : Step(176): len = 62428.1, overlap = 15.75
PHY-3002 : Step(177): len = 60905.8, overlap = 19.25
PHY-3002 : Step(178): len = 60181.3, overlap = 21
PHY-3002 : Step(179): len = 59746.4, overlap = 24.75
PHY-3002 : Step(180): len = 59430, overlap = 25.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00124825
PHY-3002 : Step(181): len = 59754.8, overlap = 25.75
PHY-3002 : Step(182): len = 59896.5, overlap = 25.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0024965
PHY-3002 : Step(183): len = 59997.1, overlap = 26.25
PHY-3002 : Step(184): len = 60078, overlap = 25.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005600s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (279.0%)

PHY-3001 : Legalized: Len = 64529.2, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006222s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 0, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 64673.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6687, tnet num: 2056, tinst num: 844, tnode num: 9052, tedge num: 11728.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 51/2058.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71168, over cnt = 137(0%), over = 204, worst = 7
PHY-1002 : len = 72144, over cnt = 63(0%), over = 76, worst = 4
PHY-1002 : len = 72928, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 73064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119139s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (144.3%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 23.31, top10 = 18.12, top15 = 14.32.
PHY-1001 : End incremental global routing;  0.173962s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (125.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065146s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.274527s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1808/2058.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006281s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (248.7%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 23.31, top10 = 18.12, top15 = 14.32.
OPT-1001 : End congestion update;  0.063723s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (122.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054297s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 806 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64738, Over = 0
PHY-3001 : End spreading;  0.005536s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64738, Over = 0
PHY-3001 : End incremental legalization;  0.036005s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (173.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.168729s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 223, reserve = 190, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055708s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1800/2058.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008954s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 23.33, top10 = 18.15, top15 = 14.34.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052883s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.929860s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (107.5%)

RUN-1003 : finish command "place" in  6.772757s wall, 10.531250s user + 3.312500s system = 13.843750s CPU (204.4%)

RUN-1004 : used memory is 200 MB, reserved memory is 166 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2058 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1501 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 84 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6687, tnet num: 2056, tinst num: 844, tnode num: 9052, tedge num: 11728.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71040, over cnt = 138(0%), over = 207, worst = 7
PHY-1002 : len = 71992, over cnt = 64(0%), over = 76, worst = 4
PHY-1002 : len = 72808, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116606s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 23.29, top10 = 18.07, top15 = 14.25.
PHY-1001 : End global routing;  0.167326s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (130.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 469, peak = 499.
PHY-1001 : End build detailed router design. 3.337520s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.462646s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 533.
PHY-1001 : End phase 1; 1.469515s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182232, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End initial routed; 1.139480s wall, 1.968750s user + 0.187500s system = 2.156250s CPU (189.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1825(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.221   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.380766s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (102.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.520339s wall, 2.343750s user + 0.203125s system = 2.546875s CPU (167.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182232, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016007s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182160, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.035732s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (262.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022261s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1825(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.221   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.400696s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.181611s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 551, reserve = 521, peak = 551.
PHY-1001 : End phase 3; 0.780455s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (108.1%)

PHY-1003 : Routed, final wirelength = 182160
PHY-1001 : Current memory(MB): used = 551, reserve = 521, peak = 551.
PHY-1001 : End export database. 0.011150s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.307205s wall, 8.109375s user + 0.281250s system = 8.390625s CPU (114.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6687, tnet num: 2056, tinst num: 844, tnode num: 9052, tedge num: 11728.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.257311s wall, 9.093750s user + 0.296875s system = 9.390625s CPU (113.7%)

RUN-1004 : used memory is 503 MB, reserved memory is 475 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      842   out of  19600    4.30%
#reg                     1079   out of  19600    5.51%
#le                      1537
  #lut only               458   out of   1537   29.80%
  #reg only               695   out of   1537   45.22%
  #lut&reg                384   out of   1537   24.98%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |621     |221     |1110    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1129   |306     |128     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |40     |32      |6       |23      |0       |0       |
|    demodu                  |Demodulation                                     |524    |110     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |164    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |11      |0       |25      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |15      |0       |30      |0       |0       |
|    integ                   |Integration                                      |138    |16      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |90     |25      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |100     |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |113    |106     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |25     |25      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |53     |53      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1465  
    #2          2       298   
    #3          3       121   
    #4          4        12   
    #5        5-10       88   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6687, tnet num: 2056, tinst num: 844, tnode num: 9052, tedge num: 11728.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2058, pip num: 14927
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1287 valid insts, and 39472 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.206287s wall, 18.187500s user + 0.093750s system = 18.281250s CPU (570.2%)

RUN-1004 : used memory is 521 MB, reserved memory is 491 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230821_151231.log"
