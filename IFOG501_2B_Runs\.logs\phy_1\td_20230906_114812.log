============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:48:12 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1640 instances
RUN-0007 : 374 luts, 991 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2210 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1653 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1638 instances, 374 luts, 991 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7820, tnet num: 2208, tinst num: 1638, tnode num: 11058, tedge num: 13208.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313997s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 586241
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1638.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 456528, overlap = 20.25
PHY-3002 : Step(2): len = 350360, overlap = 15.75
PHY-3002 : Step(3): len = 324072, overlap = 20.25
PHY-3002 : Step(4): len = 310692, overlap = 18
PHY-3002 : Step(5): len = 299568, overlap = 18
PHY-3002 : Step(6): len = 295491, overlap = 18
PHY-3002 : Step(7): len = 290366, overlap = 18
PHY-3002 : Step(8): len = 282016, overlap = 18
PHY-3002 : Step(9): len = 278034, overlap = 20.25
PHY-3002 : Step(10): len = 270664, overlap = 20.25
PHY-3002 : Step(11): len = 265071, overlap = 20.25
PHY-3002 : Step(12): len = 259742, overlap = 20.25
PHY-3002 : Step(13): len = 254965, overlap = 20.25
PHY-3002 : Step(14): len = 248502, overlap = 20.25
PHY-3002 : Step(15): len = 244427, overlap = 20.25
PHY-3002 : Step(16): len = 237095, overlap = 20.25
PHY-3002 : Step(17): len = 232653, overlap = 20.25
PHY-3002 : Step(18): len = 228266, overlap = 20.25
PHY-3002 : Step(19): len = 222815, overlap = 20.25
PHY-3002 : Step(20): len = 215920, overlap = 18
PHY-3002 : Step(21): len = 213083, overlap = 18
PHY-3002 : Step(22): len = 207455, overlap = 18
PHY-3002 : Step(23): len = 199153, overlap = 18
PHY-3002 : Step(24): len = 194823, overlap = 18
PHY-3002 : Step(25): len = 192663, overlap = 18
PHY-3002 : Step(26): len = 166799, overlap = 15.75
PHY-3002 : Step(27): len = 155348, overlap = 18
PHY-3002 : Step(28): len = 153275, overlap = 18
PHY-3002 : Step(29): len = 148252, overlap = 18
PHY-3002 : Step(30): len = 126373, overlap = 15.75
PHY-3002 : Step(31): len = 123665, overlap = 18
PHY-3002 : Step(32): len = 121970, overlap = 18
PHY-3002 : Step(33): len = 119551, overlap = 15.75
PHY-3002 : Step(34): len = 116019, overlap = 15.75
PHY-3002 : Step(35): len = 114374, overlap = 18
PHY-3002 : Step(36): len = 112520, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.20829e-05
PHY-3002 : Step(37): len = 113546, overlap = 20.25
PHY-3002 : Step(38): len = 113111, overlap = 11.25
PHY-3002 : Step(39): len = 112517, overlap = 11.25
PHY-3002 : Step(40): len = 111529, overlap = 15.75
PHY-3002 : Step(41): len = 108826, overlap = 15.75
PHY-3002 : Step(42): len = 102215, overlap = 9
PHY-3002 : Step(43): len = 99806.6, overlap = 9
PHY-3002 : Step(44): len = 97165.3, overlap = 11.25
PHY-3002 : Step(45): len = 95439.2, overlap = 11.25
PHY-3002 : Step(46): len = 93111.5, overlap = 11.25
PHY-3002 : Step(47): len = 92558.7, overlap = 6.75
PHY-3002 : Step(48): len = 91508.7, overlap = 11.25
PHY-3002 : Step(49): len = 90701.6, overlap = 9
PHY-3002 : Step(50): len = 90426.8, overlap = 9
PHY-3002 : Step(51): len = 89734.3, overlap = 6.75
PHY-3002 : Step(52): len = 88471.2, overlap = 4.5
PHY-3002 : Step(53): len = 86687.9, overlap = 6.75
PHY-3002 : Step(54): len = 82793.1, overlap = 11.75
PHY-3002 : Step(55): len = 82595.1, overlap = 11.75
PHY-3002 : Step(56): len = 81774.4, overlap = 7.375
PHY-3002 : Step(57): len = 80526, overlap = 4.75
PHY-3002 : Step(58): len = 77914.8, overlap = 16.3125
PHY-3002 : Step(59): len = 75921, overlap = 11.5
PHY-3002 : Step(60): len = 74375.9, overlap = 11.25
PHY-3002 : Step(61): len = 74137.4, overlap = 11.25
PHY-3002 : Step(62): len = 72831.1, overlap = 11.25
PHY-3002 : Step(63): len = 70759.6, overlap = 13.5
PHY-3002 : Step(64): len = 69150.6, overlap = 11.25
PHY-3002 : Step(65): len = 68322.7, overlap = 9.0625
PHY-3002 : Step(66): len = 67772.9, overlap = 11.3125
PHY-3002 : Step(67): len = 66259.1, overlap = 9
PHY-3002 : Step(68): len = 65556, overlap = 9
PHY-3002 : Step(69): len = 64981.3, overlap = 11.25
PHY-3002 : Step(70): len = 63828.4, overlap = 11.25
PHY-3002 : Step(71): len = 63465, overlap = 11.25
PHY-3002 : Step(72): len = 62904.2, overlap = 11.25
PHY-3002 : Step(73): len = 61835.9, overlap = 11.75
PHY-3002 : Step(74): len = 60929.9, overlap = 12
PHY-3002 : Step(75): len = 60451.8, overlap = 12
PHY-3002 : Step(76): len = 58956.2, overlap = 12.25
PHY-3002 : Step(77): len = 58706.9, overlap = 12.25
PHY-3002 : Step(78): len = 58396.1, overlap = 12.75
PHY-3002 : Step(79): len = 58092.9, overlap = 12.75
PHY-3002 : Step(80): len = 57839, overlap = 12.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000164166
PHY-3002 : Step(81): len = 57940.7, overlap = 12.75
PHY-3002 : Step(82): len = 58115.5, overlap = 10.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000328332
PHY-3002 : Step(83): len = 58024.3, overlap = 10.5
PHY-3002 : Step(84): len = 58084.1, overlap = 8.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007810s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073200s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00662583
PHY-3002 : Step(85): len = 61525.2, overlap = 8.5625
PHY-3002 : Step(86): len = 60589.9, overlap = 8.375
PHY-3002 : Step(87): len = 59797.4, overlap = 8.3125
PHY-3002 : Step(88): len = 58708.1, overlap = 8.125
PHY-3002 : Step(89): len = 58128.5, overlap = 8.875
PHY-3002 : Step(90): len = 57232.1, overlap = 8.625
PHY-3002 : Step(91): len = 56361.9, overlap = 7.1875
PHY-3002 : Step(92): len = 55504.4, overlap = 6.875
PHY-3002 : Step(93): len = 54735.5, overlap = 6.9375
PHY-3002 : Step(94): len = 54502.3, overlap = 7.125
PHY-3002 : Step(95): len = 54036.9, overlap = 6.875
PHY-3002 : Step(96): len = 53747.8, overlap = 7.375
PHY-3002 : Step(97): len = 53825.3, overlap = 7.59375
PHY-3002 : Step(98): len = 53381, overlap = 7.625
PHY-3002 : Step(99): len = 52503.1, overlap = 8.0625
PHY-3002 : Step(100): len = 51819.3, overlap = 8.46875
PHY-3002 : Step(101): len = 51435.8, overlap = 8.875
PHY-3002 : Step(102): len = 51218.8, overlap = 8
PHY-3002 : Step(103): len = 50980.5, overlap = 7.375
PHY-3002 : Step(104): len = 50970, overlap = 7.375
PHY-3002 : Step(105): len = 50482.3, overlap = 7.9375
PHY-3002 : Step(106): len = 50428, overlap = 8
PHY-3002 : Step(107): len = 50183.6, overlap = 8.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0132517
PHY-3002 : Step(108): len = 50189.1, overlap = 8.0625
PHY-3002 : Step(109): len = 50174.2, overlap = 8.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065852s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000130395
PHY-3002 : Step(110): len = 50860.8, overlap = 53.4375
PHY-3002 : Step(111): len = 51445.1, overlap = 46.25
PHY-3002 : Step(112): len = 51947.3, overlap = 42.4375
PHY-3002 : Step(113): len = 51746, overlap = 37.875
PHY-3002 : Step(114): len = 51438.3, overlap = 39.5
PHY-3002 : Step(115): len = 51418.2, overlap = 39.1875
PHY-3002 : Step(116): len = 51410.2, overlap = 38.125
PHY-3002 : Step(117): len = 51228.4, overlap = 37.1875
PHY-3002 : Step(118): len = 51035.3, overlap = 37.5625
PHY-3002 : Step(119): len = 50796.6, overlap = 37.9688
PHY-3002 : Step(120): len = 50786.3, overlap = 37.9062
PHY-3002 : Step(121): len = 50586.6, overlap = 38.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000260789
PHY-3002 : Step(122): len = 50552.3, overlap = 37.7188
PHY-3002 : Step(123): len = 50648.5, overlap = 37.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000521579
PHY-3002 : Step(124): len = 50909.4, overlap = 37.9688
PHY-3002 : Step(125): len = 51613.5, overlap = 38.2812
PHY-3002 : Step(126): len = 51890.3, overlap = 36.375
PHY-3002 : Step(127): len = 51948.4, overlap = 36.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7820, tnet num: 2208, tinst num: 1638, tnode num: 11058, tedge num: 13208.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.97 peak overflow 2.72
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2210.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55432, over cnt = 237(0%), over = 1009, worst = 21
PHY-1001 : End global iterations;  0.071164s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.8%)

PHY-1001 : Congestion index: top1 = 44.59, top5 = 24.97, top10 = 15.89, top15 = 11.33.
PHY-1001 : End incremental global routing;  0.120541s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067960s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.219367s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1641/2210.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55432, over cnt = 237(0%), over = 1009, worst = 21
PHY-1002 : len = 61984, over cnt = 162(0%), over = 339, worst = 21
PHY-1002 : len = 66352, over cnt = 31(0%), over = 37, worst = 4
PHY-1002 : len = 66792, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 67048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095628s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (114.4%)

PHY-1001 : Congestion index: top1 = 38.53, top5 = 25.40, top10 = 17.67, top15 = 13.17.
OPT-1001 : End congestion update;  0.139170s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057924s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199516s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.693718s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (103.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1072/1405 primitive instances ...
PHY-3001 : End packing;  0.047804s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (130.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2036 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1490 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 837 instances, 790 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52359.8, Over = 60
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2034, tinst num: 837, tnode num: 8892, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.316974s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.86657e-05
PHY-3002 : Step(128): len = 51782.9, overlap = 59.25
PHY-3002 : Step(129): len = 51277.1, overlap = 59.75
PHY-3002 : Step(130): len = 50802.4, overlap = 60.75
PHY-3002 : Step(131): len = 50578.8, overlap = 62.25
PHY-3002 : Step(132): len = 50596, overlap = 62.25
PHY-3002 : Step(133): len = 50529.3, overlap = 62.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.73315e-05
PHY-3002 : Step(134): len = 50715.7, overlap = 61.5
PHY-3002 : Step(135): len = 51125, overlap = 62.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000110679
PHY-3002 : Step(136): len = 51883.8, overlap = 60
PHY-3002 : Step(137): len = 53476.8, overlap = 57.25
PHY-3002 : Step(138): len = 53971.1, overlap = 55
PHY-3002 : Step(139): len = 53908.3, overlap = 55.75
PHY-3002 : Step(140): len = 53945.3, overlap = 54.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.081854s wall, 0.093750s user + 0.109375s system = 0.203125s CPU (248.2%)

PHY-3001 : Trial Legalized: Len = 69626.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051497s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000665977
PHY-3002 : Step(141): len = 66277.6, overlap = 9.75
PHY-3002 : Step(142): len = 63661.8, overlap = 13.5
PHY-3002 : Step(143): len = 61799.2, overlap = 17
PHY-3002 : Step(144): len = 60479.6, overlap = 16.75
PHY-3002 : Step(145): len = 59760.9, overlap = 20
PHY-3002 : Step(146): len = 59258.6, overlap = 22
PHY-3002 : Step(147): len = 58940.4, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133195
PHY-3002 : Step(148): len = 59268.3, overlap = 22.25
PHY-3002 : Step(149): len = 59238.9, overlap = 21.75
PHY-3002 : Step(150): len = 59217.2, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00266391
PHY-3002 : Step(151): len = 59432.4, overlap = 21.5
PHY-3002 : Step(152): len = 59492.3, overlap = 21.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004955s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65049.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005568s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 9, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 65081.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2034, tinst num: 837, tnode num: 8892, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71072, over cnt = 135(0%), over = 206, worst = 6
PHY-1002 : len = 72072, over cnt = 66(0%), over = 78, worst = 3
PHY-1002 : len = 72832, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 73056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136666s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (125.8%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 22.44, top10 = 17.43, top15 = 13.99.
PHY-1001 : End incremental global routing;  0.188033s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (116.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061802s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.280071s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (111.6%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1806/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007201s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 22.44, top10 = 17.43, top15 = 13.99.
OPT-1001 : End congestion update;  0.054449s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051698s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 799 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 837 instances, 790 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65114.8, Over = 0
PHY-3001 : End spreading;  0.005457s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (286.4%)

PHY-3001 : Final: Len = 65114.8, Over = 0
PHY-3001 : End incremental legalization;  0.034577s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153277s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.7%)

OPT-1001 : Current memory(MB): used = 223, reserve = 188, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049779s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1798/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008232s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.45, top10 = 17.44, top15 = 14.00.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049501s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.887230s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (102.1%)

RUN-1003 : finish command "place" in  5.678727s wall, 9.125000s user + 2.953125s system = 12.078125s CPU (212.7%)

RUN-1004 : used memory is 200 MB, reserved memory is 164 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2036 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1490 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2034, tinst num: 837, tnode num: 8892, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70504, over cnt = 142(0%), over = 210, worst = 6
PHY-1002 : len = 71272, over cnt = 86(0%), over = 107, worst = 4
PHY-1002 : len = 72592, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137689s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (113.5%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 22.37, top10 = 17.36, top15 = 13.91.
PHY-1001 : End global routing;  0.187350s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (108.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 3.321603s wall, 3.234375s user + 0.093750s system = 3.328125s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.319335s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 533.
PHY-1001 : End phase 1; 1.325144s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180592, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End initial routed; 1.051251s wall, 1.656250s user + 0.062500s system = 1.718750s CPU (163.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.536   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362076s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.413425s wall, 2.000000s user + 0.078125s system = 2.078125s CPU (147.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180592, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014568s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180552, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024085s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (129.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020996s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (148.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.536   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362721s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.175364s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.719667s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 180584
PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End export database. 0.009402s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.2%)

PHY-1001 : End detail routing;  6.974662s wall, 7.468750s user + 0.187500s system = 7.656250s CPU (109.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2034, tinst num: 837, tnode num: 8892, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.896375s wall, 8.406250s user + 0.203125s system = 8.609375s CPU (109.0%)

RUN-1004 : used memory is 505 MB, reserved memory is 479 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      829   out of  19600    4.23%
#reg                     1074   out of  19600    5.48%
#le                      1527
  #lut only               453   out of   1527   29.67%
  #reg only               698   out of   1527   45.71%
  #lut&reg                376   out of   1527   24.62%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       469
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       110
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_13.q0    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1527   |603     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1144   |319     |134     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |24      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |538    |122     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |52     |2       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |89     |33      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |96      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |27      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |94     |80      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |18     |15      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |40     |37      |0       |19      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1454  
    #2          2       307   
    #3          3       104   
    #4          4        19   
    #5        5-10       79   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2034, tinst num: 837, tnode num: 8892, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 837
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2036, pip num: 14649
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1295 valid insts, and 38950 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.948378s wall, 17.062500s user + 0.062500s system = 17.125000s CPU (580.8%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_114812.log"
