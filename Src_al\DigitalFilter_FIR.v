//////////////////////////////////////////////////////////////////////////////////
// 低通FIR数字滤波器模块
// 功能：去除高频噪声，保留有用的低频信号
// 原理：使用有限冲激响应滤波器，通过加权平均实现滤波
//////////////////////////////////////////////////////////////////////////////////

module DigitalFilter_FIR
#(
    parameter DATA_WIDTH = 12,      // 输入数据位宽
    parameter COEFF_WIDTH = 16,     // 滤波器系数位宽
    parameter FILTER_ORDER = 8,     // 滤波器阶数（抽头数量）
    parameter OUTPUT_WIDTH = 16     // 输出数据位宽
)
(
    input                           clk,        // 时钟信号
    input                           rst_n,      // 复位信号
    input                           data_valid, // 数据有效信号
    input   [DATA_WIDTH-1:0]        data_in,    // 输入数据
    output reg                      data_ready, // 输出数据准备好
    output reg [OUTPUT_WIDTH-1:0]   data_out    // 滤波后的输出数据
);

// 滤波器系数 - 低通滤波器（截止频率约为采样频率的1/8）
// 这些系数是预先计算好的，类似于"配方"
reg [COEFF_WIDTH-1:0] filter_coeff [0:FILTER_ORDER-1];

// 数据移位寄存器 - 存储历史数据
reg [DATA_WIDTH-1:0] data_shift_reg [0:FILTER_ORDER-1];

// 乘法结果存储
reg signed [DATA_WIDTH+COEFF_WIDTH-1:0] mult_result [0:FILTER_ORDER-1];

// 累加结果
reg signed [DATA_WIDTH+COEFF_WIDTH+3:0] accumulator;

integer i;

// 初始化滤波器系数（低通滤波器）
initial begin
    // 这些系数决定了滤波器的特性，就像调音台的均衡器
    filter_coeff[0] = 16'h0100;  // 系数1
    filter_coeff[1] = 16'h0300;  // 系数2
    filter_coeff[2] = 16'h0700;  // 系数3
    filter_coeff[3] = 16'h0F00;  // 系数4（中心，权重最大）
    filter_coeff[4] = 16'h0F00;  // 系数5
    filter_coeff[5] = 16'h0700;  // 系数6
    filter_coeff[6] = 16'h0300;  // 系数7
    filter_coeff[7] = 16'h0100;  // 系数8
end

// 数据移位寄存器更新 - 就像传送带，新数据进来，旧数据后移
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // 复位时清零所有历史数据
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            data_shift_reg[i] <= 0;
        end
    end
    else if (data_valid) begin
        // 新数据来了，所有数据向后移动一位
        data_shift_reg[0] <= data_in;  // 最新数据放在第一位
        for (i = 1; i < FILTER_ORDER; i = i + 1) begin
            data_shift_reg[i] <= data_shift_reg[i-1];  // 其他数据后移
        end
    end
end

// 乘法运算 - 每个历史数据乘以对应的系数
always @(posedge clk) begin
    for (i = 0; i < FILTER_ORDER; i = i + 1) begin
        // 有符号乘法：数据 × 系数
        mult_result[i] <= $signed(data_shift_reg[i]) * $signed(filter_coeff[i]);
    end
end

// 累加运算 - 把所有乘法结果加起来
always @(posedge clk) begin
    accumulator <= mult_result[0] + mult_result[1] + mult_result[2] + mult_result[3] +
                   mult_result[4] + mult_result[5] + mult_result[6] + mult_result[7];
end

// 输出数据处理 - 取合适的位数作为最终输出
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_out <= 0;
        data_ready <= 0;
    end
    else begin
        // 右移去掉小数部分，取高位作为输出
        data_out <= accumulator[DATA_WIDTH+COEFF_WIDTH+3:DATA_WIDTH+COEFF_WIDTH+3-OUTPUT_WIDTH+1];
        data_ready <= data_valid;  // 延迟几个时钟周期后输出准备好
    end
end

endmodule
