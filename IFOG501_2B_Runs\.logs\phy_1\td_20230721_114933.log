============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:49:33 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 369 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-1001 : 1640 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13178.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.293208s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (101.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 592086
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 561210, overlap = 20.25
PHY-3002 : Step(2): len = 446068, overlap = 20.25
PHY-3002 : Step(3): len = 377934, overlap = 15.75
PHY-3002 : Step(4): len = 359106, overlap = 11.25
PHY-3002 : Step(5): len = 340610, overlap = 15.75
PHY-3002 : Step(6): len = 332574, overlap = 15.75
PHY-3002 : Step(7): len = 322493, overlap = 15.75
PHY-3002 : Step(8): len = 314847, overlap = 18
PHY-3002 : Step(9): len = 308023, overlap = 20.25
PHY-3002 : Step(10): len = 301646, overlap = 20.25
PHY-3002 : Step(11): len = 290008, overlap = 20.25
PHY-3002 : Step(12): len = 285365, overlap = 20.25
PHY-3002 : Step(13): len = 279569, overlap = 20.25
PHY-3002 : Step(14): len = 271839, overlap = 20.25
PHY-3002 : Step(15): len = 264195, overlap = 20.25
PHY-3002 : Step(16): len = 261439, overlap = 20.25
PHY-3002 : Step(17): len = 251030, overlap = 20.25
PHY-3002 : Step(18): len = 243287, overlap = 20.25
PHY-3002 : Step(19): len = 238723, overlap = 20.25
PHY-3002 : Step(20): len = 235276, overlap = 20.25
PHY-3002 : Step(21): len = 226108, overlap = 20.25
PHY-3002 : Step(22): len = 221363, overlap = 20.25
PHY-3002 : Step(23): len = 217446, overlap = 20.25
PHY-3002 : Step(24): len = 213154, overlap = 20.25
PHY-3002 : Step(25): len = 204030, overlap = 20.25
PHY-3002 : Step(26): len = 201100, overlap = 20.25
PHY-3002 : Step(27): len = 197773, overlap = 20.25
PHY-3002 : Step(28): len = 185531, overlap = 20.25
PHY-3002 : Step(29): len = 174844, overlap = 20.25
PHY-3002 : Step(30): len = 173618, overlap = 20.25
PHY-3002 : Step(31): len = 158360, overlap = 18
PHY-3002 : Step(32): len = 119324, overlap = 18
PHY-3002 : Step(33): len = 115243, overlap = 20.25
PHY-3002 : Step(34): len = 111193, overlap = 20.25
PHY-3002 : Step(35): len = 110055, overlap = 20.25
PHY-3002 : Step(36): len = 106498, overlap = 20.25
PHY-3002 : Step(37): len = 103952, overlap = 20.25
PHY-3002 : Step(38): len = 101627, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.25765e-05
PHY-3002 : Step(39): len = 103447, overlap = 11.25
PHY-3002 : Step(40): len = 102930, overlap = 11.25
PHY-3002 : Step(41): len = 101153, overlap = 15.75
PHY-3002 : Step(42): len = 100636, overlap = 13.5
PHY-3002 : Step(43): len = 98435, overlap = 18
PHY-3002 : Step(44): len = 95994.5, overlap = 15.75
PHY-3002 : Step(45): len = 95496.9, overlap = 9
PHY-3002 : Step(46): len = 93607, overlap = 13.5
PHY-3002 : Step(47): len = 90931.6, overlap = 11.25
PHY-3002 : Step(48): len = 86895.7, overlap = 11.25
PHY-3002 : Step(49): len = 84652.5, overlap = 11.25
PHY-3002 : Step(50): len = 83093.3, overlap = 11.25
PHY-3002 : Step(51): len = 82735.8, overlap = 11.25
PHY-3002 : Step(52): len = 81359.5, overlap = 13.5
PHY-3002 : Step(53): len = 80768.2, overlap = 13.5
PHY-3002 : Step(54): len = 80480, overlap = 6.75
PHY-3002 : Step(55): len = 79017.9, overlap = 6.75
PHY-3002 : Step(56): len = 76875.1, overlap = 11.25
PHY-3002 : Step(57): len = 75886.1, overlap = 11.25
PHY-3002 : Step(58): len = 73984.8, overlap = 11.25
PHY-3002 : Step(59): len = 72619.6, overlap = 13.5
PHY-3002 : Step(60): len = 71699.3, overlap = 9
PHY-3002 : Step(61): len = 71259.3, overlap = 6.75
PHY-3002 : Step(62): len = 69683.5, overlap = 11.25
PHY-3002 : Step(63): len = 68600.9, overlap = 11.25
PHY-3002 : Step(64): len = 66561.6, overlap = 11.25
PHY-3002 : Step(65): len = 65707.8, overlap = 11.25
PHY-3002 : Step(66): len = 64804, overlap = 11.25
PHY-3002 : Step(67): len = 64285.1, overlap = 9
PHY-3002 : Step(68): len = 63287.5, overlap = 13.5
PHY-3002 : Step(69): len = 61399, overlap = 11.25
PHY-3002 : Step(70): len = 60579.1, overlap = 11.25
PHY-3002 : Step(71): len = 59636.9, overlap = 11.25
PHY-3002 : Step(72): len = 58688, overlap = 9
PHY-3002 : Step(73): len = 57552.7, overlap = 11.25
PHY-3002 : Step(74): len = 56517.8, overlap = 11.25
PHY-3002 : Step(75): len = 55764.1, overlap = 11.25
PHY-3002 : Step(76): len = 55324, overlap = 11.25
PHY-3002 : Step(77): len = 54045.5, overlap = 9
PHY-3002 : Step(78): len = 52960.6, overlap = 11.25
PHY-3002 : Step(79): len = 51882.2, overlap = 9
PHY-3002 : Step(80): len = 51245.3, overlap = 9
PHY-3002 : Step(81): len = 50840.4, overlap = 9
PHY-3002 : Step(82): len = 50669.4, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000145153
PHY-3002 : Step(83): len = 50985.4, overlap = 9
PHY-3002 : Step(84): len = 51074.1, overlap = 9
PHY-3002 : Step(85): len = 50969.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000290306
PHY-3002 : Step(86): len = 51168.1, overlap = 6.75
PHY-3002 : Step(87): len = 51177.5, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006672s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066532s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 54769.5, overlap = 6.3125
PHY-3002 : Step(89): len = 53959.5, overlap = 6.5625
PHY-3002 : Step(90): len = 53704.3, overlap = 6.21875
PHY-3002 : Step(91): len = 53139, overlap = 5.625
PHY-3002 : Step(92): len = 52762.9, overlap = 6.875
PHY-3002 : Step(93): len = 51962.3, overlap = 7.59375
PHY-3002 : Step(94): len = 51820.5, overlap = 8.03125
PHY-3002 : Step(95): len = 50486.4, overlap = 8.15625
PHY-3002 : Step(96): len = 49741.2, overlap = 8.28125
PHY-3002 : Step(97): len = 49155.2, overlap = 8.59375
PHY-3002 : Step(98): len = 48897.8, overlap = 9.53125
PHY-3002 : Step(99): len = 48474.2, overlap = 9.78125
PHY-3002 : Step(100): len = 48092.1, overlap = 10.9062
PHY-3002 : Step(101): len = 47909.9, overlap = 11.4688
PHY-3002 : Step(102): len = 47839.6, overlap = 11.625
PHY-3002 : Step(103): len = 47314.7, overlap = 12.5625
PHY-3002 : Step(104): len = 46893, overlap = 15.5625
PHY-3002 : Step(105): len = 46383.5, overlap = 16.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00075157
PHY-3002 : Step(106): len = 46194.7, overlap = 16.9375
PHY-3002 : Step(107): len = 46198.3, overlap = 16.75
PHY-3002 : Step(108): len = 46185.7, overlap = 16.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00150314
PHY-3002 : Step(109): len = 46212, overlap = 17.0625
PHY-3002 : Step(110): len = 46245.2, overlap = 17.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060490s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.37977e-05
PHY-3002 : Step(111): len = 46449.2, overlap = 67.6875
PHY-3002 : Step(112): len = 46641.6, overlap = 67.6875
PHY-3002 : Step(113): len = 46955, overlap = 66.75
PHY-3002 : Step(114): len = 46824.9, overlap = 66.0625
PHY-3002 : Step(115): len = 47096.3, overlap = 65.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127595
PHY-3002 : Step(116): len = 47123.9, overlap = 65.5938
PHY-3002 : Step(117): len = 48250.4, overlap = 60.0312
PHY-3002 : Step(118): len = 48745.8, overlap = 58.9062
PHY-3002 : Step(119): len = 49175.2, overlap = 53.8125
PHY-3002 : Step(120): len = 49091.3, overlap = 50.9688
PHY-3002 : Step(121): len = 49298, overlap = 49.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000255191
PHY-3002 : Step(122): len = 49277.8, overlap = 44.9375
PHY-3002 : Step(123): len = 49566.1, overlap = 42.3438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000510381
PHY-3002 : Step(124): len = 49864.2, overlap = 40.5312
PHY-3002 : Step(125): len = 51550.7, overlap = 37.6562
PHY-3002 : Step(126): len = 52675.2, overlap = 36.8125
PHY-3002 : Step(127): len = 52336.3, overlap = 32.5938
PHY-3002 : Step(128): len = 51893, overlap = 32.9375
PHY-3002 : Step(129): len = 51406.1, overlap = 32.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13178.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.59 peak overflow 3.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54848, over cnt = 248(0%), over = 1093, worst = 24
PHY-1001 : End global iterations;  0.056698s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (165.3%)

PHY-1001 : Congestion index: top1 = 42.48, top5 = 25.90, top10 = 16.47, top15 = 11.72.
PHY-1001 : End incremental global routing;  0.108217s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (115.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069849s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209456s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (111.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1721/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54848, over cnt = 248(0%), over = 1093, worst = 24
PHY-1002 : len = 59136, over cnt = 200(0%), over = 643, worst = 15
PHY-1002 : len = 64368, over cnt = 80(0%), over = 254, worst = 12
PHY-1002 : len = 67552, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 68024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105877s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 36.08, top5 = 25.62, top10 = 18.32, top15 = 13.53.
OPT-1001 : End congestion update;  0.148996s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062180s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.214104s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : End physical optimization;  0.706079s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (106.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 110 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 693 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1062/1394 primitive instances ...
PHY-3001 : End packing;  0.053201s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 833 instances, 786 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51437.8, Over = 62
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2028, tinst num: 833, tnode num: 8929, tedge num: 11543.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311977s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.48026e-05
PHY-3002 : Step(130): len = 50923.6, overlap = 63
PHY-3002 : Step(131): len = 50579, overlap = 62.75
PHY-3002 : Step(132): len = 50117.1, overlap = 65.5
PHY-3002 : Step(133): len = 50106.1, overlap = 66
PHY-3002 : Step(134): len = 49785.1, overlap = 66
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.96052e-05
PHY-3002 : Step(135): len = 50117.2, overlap = 63.75
PHY-3002 : Step(136): len = 50462.5, overlap = 61.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.60676e-05
PHY-3002 : Step(137): len = 50801.1, overlap = 59.75
PHY-3002 : Step(138): len = 51554.5, overlap = 58.75
PHY-3002 : Step(139): len = 52874.7, overlap = 56.25
PHY-3002 : Step(140): len = 53879.8, overlap = 51.25
PHY-3002 : Step(141): len = 54162.1, overlap = 50.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.091306s wall, 0.078125s user + 0.109375s system = 0.187500s CPU (205.4%)

PHY-3001 : Trial Legalized: Len = 67436.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054124s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000570785
PHY-3002 : Step(142): len = 64761.4, overlap = 5.5
PHY-3002 : Step(143): len = 62642.5, overlap = 12.25
PHY-3002 : Step(144): len = 61045.9, overlap = 15.5
PHY-3002 : Step(145): len = 59937.1, overlap = 18
PHY-3002 : Step(146): len = 59079.1, overlap = 23.25
PHY-3002 : Step(147): len = 58671.8, overlap = 25.5
PHY-3002 : Step(148): len = 58323.7, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00114157
PHY-3002 : Step(149): len = 58813.3, overlap = 26.25
PHY-3002 : Step(150): len = 58911.8, overlap = 25.75
PHY-3002 : Step(151): len = 58822.2, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00228314
PHY-3002 : Step(152): len = 58907.3, overlap = 24
PHY-3002 : Step(153): len = 58978.3, overlap = 24
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005233s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64041.3, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005958s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.2%)

PHY-3001 : 14 instances has been re-located, deltaX = 5, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 64163.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2028, tinst num: 833, tnode num: 8929, tedge num: 11543.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 95/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70632, over cnt = 139(0%), over = 194, worst = 7
PHY-1002 : len = 71312, over cnt = 84(0%), over = 94, worst = 3
PHY-1002 : len = 72264, over cnt = 19(0%), over = 19, worst = 1
PHY-1002 : len = 72576, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148414s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.3%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.22, top10 = 17.62, top15 = 14.05.
PHY-1001 : End incremental global routing;  0.204429s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (114.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063427s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.296963s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (105.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1796/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005799s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (269.4%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.22, top10 = 17.62, top15 = 14.05.
OPT-1001 : End congestion update;  0.050517s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050720s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 795 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 833 instances, 786 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64218.8, Over = 0
PHY-3001 : End spreading;  0.005926s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64218.8, Over = 0
PHY-3001 : End incremental legalization;  0.036235s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151185s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051166s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1792/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007927s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (197.1%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.21, top10 = 17.62, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048750s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.905533s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (103.5%)

RUN-1003 : finish command "place" in  5.408241s wall, 8.593750s user + 2.062500s system = 10.656250s CPU (197.0%)

RUN-1004 : used memory is 205 MB, reserved memory is 169 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2028, tinst num: 833, tnode num: 8929, tedge num: 11543.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69920, over cnt = 144(0%), over = 207, worst = 7
PHY-1002 : len = 70672, over cnt = 84(0%), over = 95, worst = 3
PHY-1002 : len = 71920, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 72008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118167s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (132.2%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.16, top10 = 17.53, top15 = 13.96.
PHY-1001 : End global routing;  0.169491s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (119.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 201, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 495, reserve = 464, peak = 495.
PHY-1001 : End build detailed router design. 3.272347s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.320480s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 527, reserve = 498, peak = 527.
PHY-1001 : End phase 1; 1.326183s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186312, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 499, peak = 529.
PHY-1001 : End initial routed; 1.125889s wall, 2.265625s user + 0.125000s system = 2.390625s CPU (212.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.633   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365966s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End phase 2; 1.491946s wall, 2.625000s user + 0.125000s system = 2.750000s CPU (184.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186312, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014730s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186288, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033215s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (141.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027700s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.633   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.371432s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (96.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.187707s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 547, reserve = 515, peak = 547.
PHY-1001 : End phase 3; 0.756292s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 186320
PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End export database. 0.010328s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.049469s wall, 8.156250s user + 0.156250s system = 8.312500s CPU (117.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2028, tinst num: 833, tnode num: 8929, tedge num: 11543.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.947731s wall, 9.078125s user + 0.171875s system = 9.250000s CPU (116.4%)

RUN-1004 : used memory is 524 MB, reserved memory is 494 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      821   out of  19600    4.19%
#reg                     1074   out of  19600    5.48%
#le                      1514
  #lut only               440   out of   1514   29.06%
  #reg only               693   out of   1514   45.77%
  #lut&reg                381   out of   1514   25.17%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1514   |596     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1119   |301     |132     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |529    |128     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |18      |0       |31      |0       |0       |
|    integ                   |Integration                                      |138    |15      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |89     |27      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |91      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |102    |88      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |45     |42      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1447  
    #2          2       308   
    #3          3       108   
    #4          4        16   
    #5        5-10       78   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2028, tinst num: 833, tnode num: 8929, tedge num: 11543.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 833
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14803
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1310 valid insts, and 39164 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.221101s wall, 18.500000s user + 0.015625s system = 18.515625s CPU (574.8%)

RUN-1004 : used memory is 548 MB, reserved memory is 516 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_114933.log"
