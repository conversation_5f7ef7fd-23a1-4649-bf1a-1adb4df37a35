============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 31 14:15:30 2023

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  4.214272s wall, 4.062500s user + 0.265625s system = 4.328125s CPU (102.7%)

RUN-1004 : used memory is 564 MB, reserved memory is 543 MB, peak memory is 564 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.236106s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (101.1%)

RUN-1004 : used memory is 578 MB, reserved memory is 557 MB, peak memory is 578 MB
