============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 17:35:06 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1531 instances
RUN-0007 : 384 luts, 894 seqs, 129 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2070 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1550 nets have 2 pins
RUN-1001 : 404 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1529 instances, 384 luts, 894 seqs, 204 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7310, tnet num: 2068, tinst num: 1529, tnode num: 10239, tedge num: 12359.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.269916s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (104.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 536552
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1529.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 447385, overlap = 15.75
PHY-3002 : Step(2): len = 414661, overlap = 20.25
PHY-3002 : Step(3): len = 390660, overlap = 20.25
PHY-3002 : Step(4): len = 377210, overlap = 20.25
PHY-3002 : Step(5): len = 370589, overlap = 20.25
PHY-3002 : Step(6): len = 360158, overlap = 18
PHY-3002 : Step(7): len = 351995, overlap = 20.25
PHY-3002 : Step(8): len = 345532, overlap = 18
PHY-3002 : Step(9): len = 336582, overlap = 20.25
PHY-3002 : Step(10): len = 329826, overlap = 18
PHY-3002 : Step(11): len = 322430, overlap = 20.25
PHY-3002 : Step(12): len = 316211, overlap = 18
PHY-3002 : Step(13): len = 308612, overlap = 20.25
PHY-3002 : Step(14): len = 301993, overlap = 15.75
PHY-3002 : Step(15): len = 295553, overlap = 18
PHY-3002 : Step(16): len = 290243, overlap = 18
PHY-3002 : Step(17): len = 283430, overlap = 18
PHY-3002 : Step(18): len = 277158, overlap = 18
PHY-3002 : Step(19): len = 272090, overlap = 18
PHY-3002 : Step(20): len = 267262, overlap = 18
PHY-3002 : Step(21): len = 260392, overlap = 18
PHY-3002 : Step(22): len = 254630, overlap = 15.75
PHY-3002 : Step(23): len = 250659, overlap = 15.75
PHY-3002 : Step(24): len = 244908, overlap = 15.75
PHY-3002 : Step(25): len = 238931, overlap = 15.75
PHY-3002 : Step(26): len = 234643, overlap = 15.75
PHY-3002 : Step(27): len = 230851, overlap = 15.75
PHY-3002 : Step(28): len = 222821, overlap = 13.5
PHY-3002 : Step(29): len = 214475, overlap = 15.75
PHY-3002 : Step(30): len = 211313, overlap = 13.5
PHY-3002 : Step(31): len = 206345, overlap = 13.5
PHY-3002 : Step(32): len = 164904, overlap = 18
PHY-3002 : Step(33): len = 158784, overlap = 15.75
PHY-3002 : Step(34): len = 157087, overlap = 15.75
PHY-3002 : Step(35): len = 131005, overlap = 11.25
PHY-3002 : Step(36): len = 128818, overlap = 13.5
PHY-3002 : Step(37): len = 126243, overlap = 11.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000154157
PHY-3002 : Step(38): len = 126603, overlap = 13.5
PHY-3002 : Step(39): len = 125798, overlap = 11.25
PHY-3002 : Step(40): len = 124473, overlap = 11.25
PHY-3002 : Step(41): len = 123234, overlap = 13.5
PHY-3002 : Step(42): len = 122094, overlap = 13.5
PHY-3002 : Step(43): len = 117460, overlap = 15.75
PHY-3002 : Step(44): len = 115094, overlap = 13.5
PHY-3002 : Step(45): len = 113648, overlap = 13.5
PHY-3002 : Step(46): len = 112447, overlap = 13.5
PHY-3002 : Step(47): len = 110114, overlap = 13.5
PHY-3002 : Step(48): len = 106524, overlap = 13.5
PHY-3002 : Step(49): len = 105331, overlap = 13.5
PHY-3002 : Step(50): len = 103564, overlap = 13.5
PHY-3002 : Step(51): len = 99966.2, overlap = 13.5
PHY-3002 : Step(52): len = 99636.2, overlap = 13.5
PHY-3002 : Step(53): len = 98230.7, overlap = 13.5
PHY-3002 : Step(54): len = 95492.5, overlap = 11.25
PHY-3002 : Step(55): len = 93240.8, overlap = 13.5
PHY-3002 : Step(56): len = 91563.6, overlap = 13.5
PHY-3002 : Step(57): len = 90560.3, overlap = 13.5
PHY-3002 : Step(58): len = 88044.1, overlap = 11.25
PHY-3002 : Step(59): len = 87026.1, overlap = 13.5
PHY-3002 : Step(60): len = 85153, overlap = 13.5
PHY-3002 : Step(61): len = 82102.4, overlap = 9
PHY-3002 : Step(62): len = 79627.1, overlap = 11.25
PHY-3002 : Step(63): len = 78908.6, overlap = 11.25
PHY-3002 : Step(64): len = 77859.7, overlap = 11.3125
PHY-3002 : Step(65): len = 77376.2, overlap = 11.5
PHY-3002 : Step(66): len = 76927.5, overlap = 11.8125
PHY-3002 : Step(67): len = 76702.9, overlap = 11.875
PHY-3002 : Step(68): len = 75519, overlap = 12.3125
PHY-3002 : Step(69): len = 74048.3, overlap = 12.8125
PHY-3002 : Step(70): len = 72981.5, overlap = 12.875
PHY-3002 : Step(71): len = 71924.5, overlap = 13.3125
PHY-3002 : Step(72): len = 70073.6, overlap = 13.625
PHY-3002 : Step(73): len = 69410.7, overlap = 11.6875
PHY-3002 : Step(74): len = 67744.2, overlap = 11.1875
PHY-3002 : Step(75): len = 66838.7, overlap = 11.4375
PHY-3002 : Step(76): len = 66080.9, overlap = 13.5625
PHY-3002 : Step(77): len = 65567.3, overlap = 13.875
PHY-3002 : Step(78): len = 65270.4, overlap = 13.625
PHY-3002 : Step(79): len = 64887, overlap = 11.4375
PHY-3002 : Step(80): len = 64698.4, overlap = 13.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000308314
PHY-3002 : Step(81): len = 64861.2, overlap = 13.6875
PHY-3002 : Step(82): len = 64778.2, overlap = 11.4375
PHY-3002 : Step(83): len = 64761, overlap = 13.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000616628
PHY-3002 : Step(84): len = 64639, overlap = 11.5625
PHY-3002 : Step(85): len = 64691.4, overlap = 11.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006403s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (488.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054188s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000721086
PHY-3002 : Step(86): len = 67245.8, overlap = 16.3438
PHY-3002 : Step(87): len = 66553.3, overlap = 16.0938
PHY-3002 : Step(88): len = 65258.8, overlap = 15.5625
PHY-3002 : Step(89): len = 63479.9, overlap = 17.3125
PHY-3002 : Step(90): len = 62439.3, overlap = 17.7812
PHY-3002 : Step(91): len = 60015.6, overlap = 14.9375
PHY-3002 : Step(92): len = 59294.1, overlap = 15.2188
PHY-3002 : Step(93): len = 58044, overlap = 13.875
PHY-3002 : Step(94): len = 57503, overlap = 14.0625
PHY-3002 : Step(95): len = 56621.1, overlap = 14.0625
PHY-3002 : Step(96): len = 56524.5, overlap = 14.3125
PHY-3002 : Step(97): len = 56213.4, overlap = 16.0938
PHY-3002 : Step(98): len = 55649.3, overlap = 14.9062
PHY-3002 : Step(99): len = 55329.4, overlap = 15.2812
PHY-3002 : Step(100): len = 54684, overlap = 16.3438
PHY-3002 : Step(101): len = 54598.7, overlap = 16.3125
PHY-3002 : Step(102): len = 54106.1, overlap = 16.375
PHY-3002 : Step(103): len = 53637.1, overlap = 16.0312
PHY-3002 : Step(104): len = 52866.3, overlap = 14.9062
PHY-3002 : Step(105): len = 52836, overlap = 15.3438
PHY-3002 : Step(106): len = 52599, overlap = 15.0938
PHY-3002 : Step(107): len = 51542.9, overlap = 14.0938
PHY-3002 : Step(108): len = 51118.7, overlap = 14.1562
PHY-3002 : Step(109): len = 50715.4, overlap = 12.7812
PHY-3002 : Step(110): len = 50680, overlap = 12.9062
PHY-3002 : Step(111): len = 50345.3, overlap = 12.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00144217
PHY-3002 : Step(112): len = 50184.4, overlap = 12.5312
PHY-3002 : Step(113): len = 50152.2, overlap = 12.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00288434
PHY-3002 : Step(114): len = 50033.8, overlap = 12.125
PHY-3002 : Step(115): len = 50068.6, overlap = 12.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060685s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.91562e-05
PHY-3002 : Step(116): len = 50375, overlap = 45.375
PHY-3002 : Step(117): len = 51660.2, overlap = 47.4062
PHY-3002 : Step(118): len = 52233.2, overlap = 45.6562
PHY-3002 : Step(119): len = 51728, overlap = 45.0312
PHY-3002 : Step(120): len = 51216.9, overlap = 45.5
PHY-3002 : Step(121): len = 50973.4, overlap = 42.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000158312
PHY-3002 : Step(122): len = 51011.9, overlap = 42.5938
PHY-3002 : Step(123): len = 51360.2, overlap = 41.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000300827
PHY-3002 : Step(124): len = 51440.3, overlap = 41.0938
PHY-3002 : Step(125): len = 53063, overlap = 34.7812
PHY-3002 : Step(126): len = 53691.5, overlap = 38.8125
PHY-3002 : Step(127): len = 54912, overlap = 35.25
PHY-3002 : Step(128): len = 55164.1, overlap = 33.9688
PHY-3002 : Step(129): len = 54463.8, overlap = 26.875
PHY-3002 : Step(130): len = 53454.6, overlap = 27.5312
PHY-3002 : Step(131): len = 53256.3, overlap = 26.9688
PHY-3002 : Step(132): len = 52985, overlap = 26.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000601653
PHY-3002 : Step(133): len = 53135.6, overlap = 27.7812
PHY-3002 : Step(134): len = 53453.4, overlap = 28.0938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00120331
PHY-3002 : Step(135): len = 53369.1, overlap = 28.5
PHY-3002 : Step(136): len = 53352.1, overlap = 28.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7310, tnet num: 2068, tinst num: 1529, tnode num: 10239, tedge num: 12359.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.31 peak overflow 2.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2070.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56408, over cnt = 240(0%), over = 1005, worst = 15
PHY-1001 : End global iterations;  0.074192s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (126.4%)

PHY-1001 : Congestion index: top1 = 41.27, top5 = 25.27, top10 = 16.26, top15 = 11.50.
PHY-1001 : End incremental global routing;  0.124055s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (113.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061844s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (101.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215514s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (108.8%)

OPT-1001 : Current memory(MB): used = 213, reserve = 176, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1561/2070.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56408, over cnt = 240(0%), over = 1005, worst = 15
PHY-1002 : len = 62184, over cnt = 168(0%), over = 396, worst = 13
PHY-1002 : len = 65496, over cnt = 48(0%), over = 129, worst = 13
PHY-1002 : len = 66592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092549s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (135.1%)

PHY-1001 : Congestion index: top1 = 35.95, top5 = 24.84, top10 = 17.78, top15 = 13.07.
OPT-1001 : End congestion update;  0.135084s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (127.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052449s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.190689s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 216, reserve = 178, peak = 216.
OPT-1001 : End physical optimization;  0.685896s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (107.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 723 remaining SEQ's ...
SYN-4005 : Packed 115 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 608 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 992/1279 primitive instances ...
PHY-3001 : End packing;  0.045850s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 776 instances
RUN-1001 : 363 mslices, 364 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 774 instances, 727 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53939.8, Over = 52.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6176, tnet num: 1905, tinst num: 774, tnode num: 8301, tedge num: 10841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.289848s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.1597e-05
PHY-3002 : Step(137): len = 53323, overlap = 51.75
PHY-3002 : Step(138): len = 52992.8, overlap = 54
PHY-3002 : Step(139): len = 52569.9, overlap = 56
PHY-3002 : Step(140): len = 52442.2, overlap = 56.25
PHY-3002 : Step(141): len = 52182.3, overlap = 56.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.3194e-05
PHY-3002 : Step(142): len = 52497.8, overlap = 55.25
PHY-3002 : Step(143): len = 53311.6, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000126388
PHY-3002 : Step(144): len = 53965.6, overlap = 53.5
PHY-3002 : Step(145): len = 55002.5, overlap = 52.5
PHY-3002 : Step(146): len = 55648.9, overlap = 51.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.065402s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (167.2%)

PHY-3001 : Trial Legalized: Len = 67649
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048023s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000760092
PHY-3002 : Step(147): len = 64868.5, overlap = 6.25
PHY-3002 : Step(148): len = 62715.3, overlap = 11
PHY-3002 : Step(149): len = 61124.9, overlap = 15.25
PHY-3002 : Step(150): len = 59922, overlap = 19.5
PHY-3002 : Step(151): len = 59366.6, overlap = 21.75
PHY-3002 : Step(152): len = 59174.8, overlap = 24
PHY-3002 : Step(153): len = 58856.4, overlap = 23
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00152018
PHY-3002 : Step(154): len = 59134.8, overlap = 22.75
PHY-3002 : Step(155): len = 59270, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00304037
PHY-3002 : Step(156): len = 59440.1, overlap = 22.25
PHY-3002 : Step(157): len = 59440.1, overlap = 22.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005129s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63623.8, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005097s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 4, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 63693.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6176, tnet num: 1905, tinst num: 774, tnode num: 8301, tedge num: 10841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 183/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70920, over cnt = 133(0%), over = 207, worst = 7
PHY-1002 : len = 71592, over cnt = 75(0%), over = 99, worst = 5
PHY-1002 : len = 72688, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128334s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.90, top10 = 17.90, top15 = 14.01.
PHY-1001 : End incremental global routing;  0.179602s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (113.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054384s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.262362s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (107.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005762s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.90, top10 = 17.90, top15 = 14.01.
OPT-1001 : End congestion update;  0.050454s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053948s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 736 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 774 instances, 727 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63740.8, Over = 0
PHY-3001 : End spreading;  0.004585s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (340.8%)

PHY-3001 : Final: Len = 63740.8, Over = 0
PHY-3001 : End incremental legalization;  0.032459s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149998s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.8%)

OPT-1001 : Current memory(MB): used = 224, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047174s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (132.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1688/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008948s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.90, top10 = 17.91, top15 = 14.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046894s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.851445s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (99.1%)

RUN-1003 : finish command "place" in  5.261349s wall, 8.390625s user + 2.593750s system = 10.984375s CPU (208.8%)

RUN-1004 : used memory is 201 MB, reserved memory is 163 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 776 instances
RUN-1001 : 363 mslices, 364 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6176, tnet num: 1905, tinst num: 774, tnode num: 8301, tedge num: 10841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 363 mslices, 364 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69304, over cnt = 153(0%), over = 229, worst = 7
PHY-1002 : len = 70208, over cnt = 87(0%), over = 116, worst = 5
PHY-1002 : len = 71360, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 71720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134710s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.70, top10 = 17.65, top15 = 13.83.
PHY-1001 : End global routing;  0.182941s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (128.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 202, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 465, peak = 498.
PHY-1001 : End build detailed router design. 3.191782s wall, 3.156250s user + 0.031250s system = 3.187500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.305278s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End phase 1; 1.311267s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183016, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 499, peak = 533.
PHY-1001 : End initial routed; 1.359985s wall, 1.984375s user + 0.046875s system = 2.031250s CPU (149.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.349   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.432   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.360125s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 536, reserve = 503, peak = 536.
PHY-1001 : End phase 2; 1.720201s wall, 2.343750s user + 0.046875s system = 2.390625s CPU (139.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183016, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014566s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182792, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025796s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (121.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182816, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019611s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.349   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.432   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350714s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.160526s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End phase 3; 0.699220s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 182816
PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End export database. 0.009764s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.0%)

PHY-1001 : End detail routing;  7.109547s wall, 7.687500s user + 0.093750s system = 7.781250s CPU (109.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6176, tnet num: 1905, tinst num: 774, tnode num: 8301, tedge num: 10841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6216, tnet num: 1925, tinst num: 794, tnode num: 8341, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.946563s wall, 2.984375s user + 0.125000s system = 3.109375s CPU (105.5%)

RUN-1003 : finish command "route" in  10.565873s wall, 11.203125s user + 0.250000s system = 11.453125s CPU (108.4%)

RUN-1004 : used memory is 503 MB, reserved memory is 469 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      833   out of  19600    4.25%
#reg                      961   out of  19600    4.90%
#le                      1441
  #lut only               480   out of   1441   33.31%
  #reg only               608   out of   1441   42.19%
  #lut&reg                353   out of   1441   24.50%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         423
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1441   |629     |204     |992     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1030   |310     |112     |805     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |20      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |469    |126     |44      |345     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |31      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |5       |0       |13      |0       |0       |
|    integ                   |Integration                                      |135    |30      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |65     |17      |13      |62      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |93      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |120    |113     |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |20      |0       |0       |
|    U2                      |Ctrl_Data                                        |68     |68      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1376  
    #2          2       263   
    #3          3       124   
    #4          4        12   
    #5        5-10       82   
    #6        11-50      26   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6216, tnet num: 1925, tinst num: 794, tnode num: 8341, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1925 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 794
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1927, pip num: 14359
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1294 valid insts, and 37957 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.054066s wall, 17.125000s user + 0.093750s system = 17.218750s CPU (563.8%)

RUN-1004 : used memory is 523 MB, reserved memory is 490 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_173505.log"
