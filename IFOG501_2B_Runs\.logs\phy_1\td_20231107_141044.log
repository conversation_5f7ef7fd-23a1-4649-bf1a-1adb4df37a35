============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Nov  7 14:10:44 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1527 instances
RUN-0007 : 374 luts, 906 seqs, 123 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2056 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1540 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     253     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1525 instances, 374 luts, 906 seqs, 198 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7254, tnet num: 2054, tinst num: 1525, tnode num: 10188, tedge num: 12246.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2054 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.271851s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540417
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1525.
PHY-3001 : End clustering;  0.000037s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 445548, overlap = 20.25
PHY-3002 : Step(2): len = 413276, overlap = 18
PHY-3002 : Step(3): len = 399500, overlap = 20.25
PHY-3002 : Step(4): len = 379510, overlap = 20.25
PHY-3002 : Step(5): len = 372276, overlap = 20.25
PHY-3002 : Step(6): len = 352241, overlap = 20.25
PHY-3002 : Step(7): len = 342060, overlap = 20.25
PHY-3002 : Step(8): len = 335522, overlap = 20.25
PHY-3002 : Step(9): len = 329892, overlap = 20.25
PHY-3002 : Step(10): len = 319270, overlap = 20.25
PHY-3002 : Step(11): len = 310143, overlap = 20.25
PHY-3002 : Step(12): len = 305512, overlap = 20.25
PHY-3002 : Step(13): len = 296877, overlap = 20.25
PHY-3002 : Step(14): len = 287392, overlap = 18
PHY-3002 : Step(15): len = 282724, overlap = 18
PHY-3002 : Step(16): len = 277886, overlap = 18
PHY-3002 : Step(17): len = 269504, overlap = 18
PHY-3002 : Step(18): len = 264071, overlap = 18
PHY-3002 : Step(19): len = 260343, overlap = 18
PHY-3002 : Step(20): len = 252685, overlap = 18
PHY-3002 : Step(21): len = 247314, overlap = 20.25
PHY-3002 : Step(22): len = 243650, overlap = 20.25
PHY-3002 : Step(23): len = 235072, overlap = 20.25
PHY-3002 : Step(24): len = 230167, overlap = 20.25
PHY-3002 : Step(25): len = 227135, overlap = 20.25
PHY-3002 : Step(26): len = 216288, overlap = 20.25
PHY-3002 : Step(27): len = 208830, overlap = 20.25
PHY-3002 : Step(28): len = 206448, overlap = 20.25
PHY-3002 : Step(29): len = 197604, overlap = 18
PHY-3002 : Step(30): len = 151147, overlap = 20.25
PHY-3002 : Step(31): len = 146425, overlap = 20.25
PHY-3002 : Step(32): len = 145548, overlap = 20.25
PHY-3002 : Step(33): len = 132566, overlap = 18
PHY-3002 : Step(34): len = 129156, overlap = 20.25
PHY-3002 : Step(35): len = 127649, overlap = 20.25
PHY-3002 : Step(36): len = 124221, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108882
PHY-3002 : Step(37): len = 124692, overlap = 15.75
PHY-3002 : Step(38): len = 123711, overlap = 18
PHY-3002 : Step(39): len = 122144, overlap = 15.75
PHY-3002 : Step(40): len = 120950, overlap = 15.75
PHY-3002 : Step(41): len = 118866, overlap = 15.75
PHY-3002 : Step(42): len = 115124, overlap = 15.75
PHY-3002 : Step(43): len = 112961, overlap = 15.75
PHY-3002 : Step(44): len = 111156, overlap = 15.75
PHY-3002 : Step(45): len = 109855, overlap = 13.5
PHY-3002 : Step(46): len = 106249, overlap = 11.25
PHY-3002 : Step(47): len = 103661, overlap = 13.5
PHY-3002 : Step(48): len = 101505, overlap = 13.5
PHY-3002 : Step(49): len = 99174.8, overlap = 18
PHY-3002 : Step(50): len = 95775.6, overlap = 18
PHY-3002 : Step(51): len = 94004.4, overlap = 15.75
PHY-3002 : Step(52): len = 92703.4, overlap = 15.75
PHY-3002 : Step(53): len = 91003.4, overlap = 9
PHY-3002 : Step(54): len = 90174.9, overlap = 11.25
PHY-3002 : Step(55): len = 87752, overlap = 11.25
PHY-3002 : Step(56): len = 83794.7, overlap = 15.75
PHY-3002 : Step(57): len = 80834.9, overlap = 15.75
PHY-3002 : Step(58): len = 80623, overlap = 13.5
PHY-3002 : Step(59): len = 79460.9, overlap = 11.8125
PHY-3002 : Step(60): len = 78221, overlap = 9.5625
PHY-3002 : Step(61): len = 77338.2, overlap = 12.125
PHY-3002 : Step(62): len = 75715.7, overlap = 18.75
PHY-3002 : Step(63): len = 75034.6, overlap = 18.75
PHY-3002 : Step(64): len = 74569.3, overlap = 16.375
PHY-3002 : Step(65): len = 73353.1, overlap = 14.3125
PHY-3002 : Step(66): len = 71998.7, overlap = 12
PHY-3002 : Step(67): len = 71114.2, overlap = 12
PHY-3002 : Step(68): len = 69966.5, overlap = 14.1875
PHY-3002 : Step(69): len = 68509.8, overlap = 16.4375
PHY-3002 : Step(70): len = 66687.3, overlap = 19.125
PHY-3002 : Step(71): len = 65673.4, overlap = 14.875
PHY-3002 : Step(72): len = 64308.1, overlap = 17.5625
PHY-3002 : Step(73): len = 63867.8, overlap = 15.3125
PHY-3002 : Step(74): len = 63192.8, overlap = 13.4375
PHY-3002 : Step(75): len = 61789.7, overlap = 15.5
PHY-3002 : Step(76): len = 61446.5, overlap = 17.75
PHY-3002 : Step(77): len = 59908.6, overlap = 17.6875
PHY-3002 : Step(78): len = 59294.9, overlap = 17.8125
PHY-3002 : Step(79): len = 59314.9, overlap = 17.625
PHY-3002 : Step(80): len = 59302.9, overlap = 15.375
PHY-3002 : Step(81): len = 58734, overlap = 13.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217764
PHY-3002 : Step(82): len = 58970.3, overlap = 15.4375
PHY-3002 : Step(83): len = 58980.6, overlap = 15.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000435527
PHY-3002 : Step(84): len = 58945.8, overlap = 15.5
PHY-3002 : Step(85): len = 58956.5, overlap = 15.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005453s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2054 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054430s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000530375
PHY-3002 : Step(86): len = 62943.5, overlap = 14.9688
PHY-3002 : Step(87): len = 62680.8, overlap = 13.3125
PHY-3002 : Step(88): len = 61110.4, overlap = 13.3438
PHY-3002 : Step(89): len = 59789.1, overlap = 12.9688
PHY-3002 : Step(90): len = 58371.6, overlap = 11.4062
PHY-3002 : Step(91): len = 57122.5, overlap = 12.2188
PHY-3002 : Step(92): len = 56334.4, overlap = 12.7812
PHY-3002 : Step(93): len = 55402.3, overlap = 12.5312
PHY-3002 : Step(94): len = 54468.9, overlap = 11.6875
PHY-3002 : Step(95): len = 53571.7, overlap = 12.75
PHY-3002 : Step(96): len = 52963.6, overlap = 11.8125
PHY-3002 : Step(97): len = 52428.3, overlap = 12.7812
PHY-3002 : Step(98): len = 51359.5, overlap = 11.4375
PHY-3002 : Step(99): len = 50964.8, overlap = 12.8125
PHY-3002 : Step(100): len = 50478.2, overlap = 14.8438
PHY-3002 : Step(101): len = 49949, overlap = 15.25
PHY-3002 : Step(102): len = 49164.8, overlap = 15.4375
PHY-3002 : Step(103): len = 48705, overlap = 15.4688
PHY-3002 : Step(104): len = 48382, overlap = 15.4688
PHY-3002 : Step(105): len = 47775.4, overlap = 15.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00106075
PHY-3002 : Step(106): len = 47828.5, overlap = 15.6562
PHY-3002 : Step(107): len = 47873.9, overlap = 15.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0021215
PHY-3002 : Step(108): len = 47644.5, overlap = 15.5938
PHY-3002 : Step(109): len = 47644.5, overlap = 15.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2054 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059324s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.05858e-05
PHY-3002 : Step(110): len = 48798.2, overlap = 45.3438
PHY-3002 : Step(111): len = 49304, overlap = 47.1562
PHY-3002 : Step(112): len = 49492.1, overlap = 39.9375
PHY-3002 : Step(113): len = 49343.3, overlap = 37.1875
PHY-3002 : Step(114): len = 48955.1, overlap = 33.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000161172
PHY-3002 : Step(115): len = 49319.4, overlap = 33.0312
PHY-3002 : Step(116): len = 50159.4, overlap = 31.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000322343
PHY-3002 : Step(117): len = 49880, overlap = 34.4062
PHY-3002 : Step(118): len = 50271, overlap = 33.625
PHY-3002 : Step(119): len = 51294.3, overlap = 30.3438
PHY-3002 : Step(120): len = 51584.7, overlap = 29.1562
PHY-3002 : Step(121): len = 52155.8, overlap = 28.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7254, tnet num: 2054, tinst num: 1525, tnode num: 10188, tedge num: 12246.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.44 peak overflow 2.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2056.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54560, over cnt = 269(0%), over = 1070, worst = 19
PHY-1001 : End global iterations;  0.077680s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (120.7%)

PHY-1001 : Congestion index: top1 = 43.84, top5 = 26.18, top10 = 16.17, top15 = 11.34.
PHY-1001 : End incremental global routing;  0.126515s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (111.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2054 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061974s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217701s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (107.7%)

OPT-1001 : Current memory(MB): used = 210, reserve = 174, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1584/2056.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54560, over cnt = 269(0%), over = 1070, worst = 19
PHY-1002 : len = 62000, over cnt = 170(0%), over = 345, worst = 13
PHY-1002 : len = 63720, over cnt = 97(0%), over = 167, worst = 13
PHY-1002 : len = 66320, over cnt = 23(0%), over = 28, worst = 3
PHY-1002 : len = 67000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117963s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (132.5%)

PHY-1001 : Congestion index: top1 = 38.75, top5 = 26.02, top10 = 18.16, top15 = 13.22.
OPT-1001 : End congestion update;  0.159416s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (127.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2054 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055430s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.218532s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (121.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : End physical optimization;  0.712314s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (107.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 174 SEQ to BLE.
SYN-4003 : Packing 732 remaining SEQ's ...
SYN-4005 : Packed 124 SEQ with LUT/SLICE
SYN-4006 : 98 single LUT's are left
SYN-4006 : 608 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 982/1263 primitive instances ...
PHY-3001 : End packing;  0.044591s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 765 instances
RUN-1001 : 358 mslices, 358 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 763 instances, 716 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52269.4, Over = 54.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6110, tnet num: 1888, tinst num: 763, tnode num: 8220, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.293850s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (95.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.92364e-05
PHY-3002 : Step(122): len = 51698.4, overlap = 56
PHY-3002 : Step(123): len = 50824.2, overlap = 55.75
PHY-3002 : Step(124): len = 50452, overlap = 56.75
PHY-3002 : Step(125): len = 50773.8, overlap = 57
PHY-3002 : Step(126): len = 50560.7, overlap = 57.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.84728e-05
PHY-3002 : Step(127): len = 50958.2, overlap = 57
PHY-3002 : Step(128): len = 52074.4, overlap = 53
PHY-3002 : Step(129): len = 52172.3, overlap = 52.25
PHY-3002 : Step(130): len = 51970.4, overlap = 51
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116946
PHY-3002 : Step(131): len = 53000.2, overlap = 48.5
PHY-3002 : Step(132): len = 53958.1, overlap = 47.25
PHY-3002 : Step(133): len = 54471.8, overlap = 46.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088892s wall, 0.062500s user + 0.156250s system = 0.218750s CPU (246.1%)

PHY-3001 : Trial Legalized: Len = 67125.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046676s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000856478
PHY-3002 : Step(134): len = 63754.6, overlap = 4.5
PHY-3002 : Step(135): len = 61905, overlap = 10.5
PHY-3002 : Step(136): len = 60394.4, overlap = 12
PHY-3002 : Step(137): len = 59545.5, overlap = 14.25
PHY-3002 : Step(138): len = 58812.3, overlap = 15.5
PHY-3002 : Step(139): len = 58361, overlap = 18
PHY-3002 : Step(140): len = 58171.7, overlap = 17
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00170581
PHY-3002 : Step(141): len = 58430.8, overlap = 16
PHY-3002 : Step(142): len = 58569.3, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00341162
PHY-3002 : Step(143): len = 58792.2, overlap = 15.5
PHY-3002 : Step(144): len = 58952.8, overlap = 15.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005163s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62799, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004870s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (320.9%)

PHY-3001 : 7 instances has been re-located, deltaX = 0, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 62863, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6110, tnet num: 1888, tinst num: 763, tnode num: 8220, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 21/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69008, over cnt = 145(0%), over = 221, worst = 7
PHY-1002 : len = 70184, over cnt = 63(0%), over = 80, worst = 4
PHY-1002 : len = 71216, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129502s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.5%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 23.05, top10 = 17.82, top15 = 13.72.
PHY-1001 : End incremental global routing;  0.179421s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059891s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.268896s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 180, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1673/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006721s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (232.5%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 23.05, top10 = 17.82, top15 = 13.72.
OPT-1001 : End congestion update;  0.054163s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052055s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 725 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 763 instances, 716 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62920.4, Over = 0
PHY-3001 : End spreading;  0.004798s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62920.4, Over = 0
PHY-3001 : End incremental legalization;  0.034381s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155161s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (151.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044044s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1669/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006877s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 23.03, top10 = 17.81, top15 = 13.71.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046267s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.847347s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (108.8%)

RUN-1003 : finish command "place" in  5.206831s wall, 7.906250s user + 2.953125s system = 10.859375s CPU (208.6%)

RUN-1004 : used memory is 200 MB, reserved memory is 164 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 765 instances
RUN-1001 : 358 mslices, 358 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1366 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6110, tnet num: 1888, tinst num: 763, tnode num: 8220, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 358 mslices, 358 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68608, over cnt = 150(0%), over = 220, worst = 7
PHY-1002 : len = 69680, over cnt = 66(0%), over = 83, worst = 4
PHY-1002 : len = 70400, over cnt = 25(0%), over = 29, worst = 2
PHY-1002 : len = 70912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129172s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.9%)

PHY-1001 : Congestion index: top1 = 31.31, top5 = 22.94, top10 = 17.69, top15 = 13.63.
PHY-1001 : End global routing;  0.177587s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (105.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 199, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 464, peak = 495.
PHY-1001 : End build detailed router design. 3.262946s wall, 3.218750s user + 0.046875s system = 3.265625s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32296, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.308978s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 527, reserve = 497, peak = 527.
PHY-1001 : End phase 1; 1.314682s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181784, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 530.
PHY-1001 : End initial routed; 1.282219s wall, 2.312500s user + 0.109375s system = 2.421875s CPU (188.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1677(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.250   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.351878s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End phase 2; 1.634182s wall, 2.671875s user + 0.109375s system = 2.781250s CPU (170.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181784, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014884s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181880, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026481s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (59.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181992, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021515s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (72.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181968, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019651s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (159.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1677(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.250   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.348973s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.169445s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 544, reserve = 512, peak = 544.
PHY-1001 : End phase 3; 0.734929s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 181968
PHY-1001 : Current memory(MB): used = 544, reserve = 513, peak = 544.
PHY-1001 : End export database. 0.010060s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (155.3%)

PHY-1001 : End detail routing;  7.133071s wall, 8.109375s user + 0.156250s system = 8.265625s CPU (115.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6110, tnet num: 1888, tinst num: 763, tnode num: 8220, tedge num: 10725.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6138, tnet num: 1902, tinst num: 777, tnode num: 8248, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.983104s wall, 2.953125s user + 0.234375s system = 3.187500s CPU (106.9%)

RUN-1003 : finish command "route" in  10.631038s wall, 11.578125s user + 0.390625s system = 11.968750s CPU (112.6%)

RUN-1004 : used memory is 524 MB, reserved memory is 492 MB, peak memory is 544 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      803   out of  19600    4.10%
#reg                      961   out of  19600    4.90%
#le                      1411
  #lut only               450   out of   1411   31.89%
  #reg only               608   out of   1411   43.09%
  #lut&reg                353   out of   1411   25.02%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         416
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1411   |605     |198     |992     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1005   |292     |105     |806     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |456    |124     |44      |348     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |33      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |9       |0       |14      |0       |0       |
|    integ                   |Integration                                      |136    |20      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |61     |22      |7       |59      |0       |1       |
|    rs422                   |Rs422Output                                      |301    |86      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |115    |108     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |20      |0       |0       |
|    U2                      |Ctrl_Data                                        |63     |63      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1344  
    #2          2       278   
    #3          3       119   
    #4          4        11   
    #5        5-10       81   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6138, tnet num: 1902, tinst num: 777, tnode num: 8248, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1902 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 777
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1904, pip num: 14218
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1323 valid insts, and 37531 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.112201s wall, 18.140625s user + 0.046875s system = 18.187500s CPU (584.4%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231107_141044.log"
