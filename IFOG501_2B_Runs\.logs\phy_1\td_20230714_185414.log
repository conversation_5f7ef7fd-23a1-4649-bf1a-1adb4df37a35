============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 14 18:54:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(72)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(125)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[1]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[1]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[1]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[1] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 373662154752"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 382252089344"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../cwc.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 74 trigger nets, 74 data nets.
KIT-1004 : Chipwatcher code = 0011010101101111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=198) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=198) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0110111) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=198)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=198)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0110111)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 4372/27 useful/useless nets, 2586/19 useful/useless insts
SYN-1016 : Merged 37 instances.
SYN-1032 : 3888/26 useful/useless nets, 3180/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 574 better
SYN-1014 : Optimize round 2
SYN-1032 : 3444/30 useful/useless nets, 2736/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 3476/294 useful/useless nets, 2800/78 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-2571 : Optimize after map_dsp, round 1, 402 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 69 instances.
SYN-2501 : Optimize round 1, 140 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 4094/4 useful/useless nets, 3418/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 15421, tnet num: 4094, tinst num: 3417, tnode num: 20728, tedge num: 24479.
TMR-2508 : Levelizing timing graph completed, there are 131 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 4094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 351 (3.27), #lev = 7 (1.58)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 346 (3.27), #lev = 7 (1.63)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 737 instances into 346 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 569 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 182 adder to BLE ...
SYN-4008 : Packed 182 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  1.094030s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.0%)

RUN-1004 : used memory is 191 MB, reserved memory is 150 MB, peak memory is 230 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  2.172000s wall, 1.953125s user + 0.171875s system = 2.125000s CPU (97.8%)

RUN-1004 : used memory is 191 MB, reserved memory is 150 MB, peak memory is 230 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (412 clock/control pins, 0 other pins).
SYN-4027 : Net TxTransmit_dup_1 is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net RxTransmit_dup_1 is clkc1 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net RxTransmit_dup_1 as clock net
SYN-4025 : Tag rtl::Net TxTransmit_dup_1 as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2758 instances
RUN-0007 : 709 luts, 1598 seqs, 245 mslices, 121 lslices, 34 pads, 40 brams, 5 dsps
RUN-1001 : There are total 3436 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 2150 nets have 2 pins
RUN-1001 : 1049 nets have [3 - 5] pins
RUN-1001 : 147 nets have [6 - 10] pins
RUN-1001 : 45 nets have [11 - 20] pins
RUN-1001 : 32 nets have [21 - 99] pins
RUN-1001 : 12 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     257     
RUN-1001 :   No   |  No   |  Yes  |     377     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     315     
RUN-1001 :   Yes  |  No   |  Yes  |     539     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    5    |  20   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 29
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2756 instances, 709 luts, 1598 seqs, 366 slices, 40 macros(366 instances: 245 mslices 121 lslices)
PHY-0007 : Cell area utilization is 8%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13969, tnet num: 3434, tinst num: 2756, tnode num: 19677, tedge num: 23533.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.410896s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (98.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 933857
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2756.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 792905, overlap = 101.25
PHY-3002 : Step(2): len = 652537, overlap = 99
PHY-3002 : Step(3): len = 613476, overlap = 101.25
PHY-3002 : Step(4): len = 591173, overlap = 96.75
PHY-3002 : Step(5): len = 569657, overlap = 96.75
PHY-3002 : Step(6): len = 554635, overlap = 96.75
PHY-3002 : Step(7): len = 536908, overlap = 96.75
PHY-3002 : Step(8): len = 521753, overlap = 96.75
PHY-3002 : Step(9): len = 509479, overlap = 96.75
PHY-3002 : Step(10): len = 492815, overlap = 96.75
PHY-3002 : Step(11): len = 476927, overlap = 96.75
PHY-3002 : Step(12): len = 468537, overlap = 96.75
PHY-3002 : Step(13): len = 448321, overlap = 96.75
PHY-3002 : Step(14): len = 430324, overlap = 94.5
PHY-3002 : Step(15): len = 422730, overlap = 94.5
PHY-3002 : Step(16): len = 410572, overlap = 94.5
PHY-3002 : Step(17): len = 380087, overlap = 96.75
PHY-3002 : Step(18): len = 369455, overlap = 92.25
PHY-3002 : Step(19): len = 363147, overlap = 94.5
PHY-3002 : Step(20): len = 296420, overlap = 96.75
PHY-3002 : Step(21): len = 277221, overlap = 90
PHY-3002 : Step(22): len = 272929, overlap = 92.25
PHY-3002 : Step(23): len = 270497, overlap = 87.75
PHY-3002 : Step(24): len = 259519, overlap = 94.5
PHY-3002 : Step(25): len = 250767, overlap = 94.5
PHY-3002 : Step(26): len = 246404, overlap = 96.75
PHY-3002 : Step(27): len = 239108, overlap = 96.75
PHY-3002 : Step(28): len = 231936, overlap = 94.5
PHY-3002 : Step(29): len = 226027, overlap = 94.5
PHY-3002 : Step(30): len = 217108, overlap = 94.625
PHY-3002 : Step(31): len = 211663, overlap = 96.875
PHY-3002 : Step(32): len = 206368, overlap = 94.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.12493e-05
PHY-3002 : Step(33): len = 218291, overlap = 90.125
PHY-3002 : Step(34): len = 222409, overlap = 90
PHY-3002 : Step(35): len = 214873, overlap = 74.25
PHY-3002 : Step(36): len = 211409, overlap = 76.5
PHY-3002 : Step(37): len = 208533, overlap = 76.5
PHY-3002 : Step(38): len = 201629, overlap = 74.25
PHY-3002 : Step(39): len = 199667, overlap = 85.5
PHY-3002 : Step(40): len = 197914, overlap = 78.75
PHY-3002 : Step(41): len = 191077, overlap = 67.5
PHY-3002 : Step(42): len = 186664, overlap = 65.25
PHY-3002 : Step(43): len = 181402, overlap = 74.4375
PHY-3002 : Step(44): len = 177956, overlap = 81.1875
PHY-3002 : Step(45): len = 173030, overlap = 76.625
PHY-3002 : Step(46): len = 168461, overlap = 83.8125
PHY-3002 : Step(47): len = 164310, overlap = 86.1875
PHY-3002 : Step(48): len = 162379, overlap = 77.625
PHY-3002 : Step(49): len = 158852, overlap = 76.1875
PHY-3002 : Step(50): len = 154583, overlap = 71.5625
PHY-3002 : Step(51): len = 150095, overlap = 71.3125
PHY-3002 : Step(52): len = 147305, overlap = 73.8125
PHY-3002 : Step(53): len = 143977, overlap = 74.25
PHY-3002 : Step(54): len = 139657, overlap = 81.375
PHY-3002 : Step(55): len = 134086, overlap = 82.7188
PHY-3002 : Step(56): len = 133114, overlap = 83.1875
PHY-3002 : Step(57): len = 130210, overlap = 81.75
PHY-3002 : Step(58): len = 127425, overlap = 73.6875
PHY-3002 : Step(59): len = 125101, overlap = 78.875
PHY-3002 : Step(60): len = 119960, overlap = 81.1875
PHY-3002 : Step(61): len = 116411, overlap = 86
PHY-3002 : Step(62): len = 115395, overlap = 83.8438
PHY-3002 : Step(63): len = 114037, overlap = 84.5
PHY-3002 : Step(64): len = 113564, overlap = 85.1875
PHY-3002 : Step(65): len = 112360, overlap = 90.1562
PHY-3002 : Step(66): len = 110352, overlap = 93.4375
PHY-3002 : Step(67): len = 109554, overlap = 91.5
PHY-3002 : Step(68): len = 106110, overlap = 93.0625
PHY-3002 : Step(69): len = 104330, overlap = 99.4375
PHY-3002 : Step(70): len = 103065, overlap = 101.406
PHY-3002 : Step(71): len = 101030, overlap = 96.9688
PHY-3002 : Step(72): len = 99956, overlap = 101.125
PHY-3002 : Step(73): len = 98028.4, overlap = 100.719
PHY-3002 : Step(74): len = 96793.6, overlap = 100.062
PHY-3002 : Step(75): len = 94492.9, overlap = 95.2812
PHY-3002 : Step(76): len = 92342, overlap = 96.25
PHY-3002 : Step(77): len = 90319.5, overlap = 96.9062
PHY-3002 : Step(78): len = 89720.4, overlap = 94.8438
PHY-3002 : Step(79): len = 87824.6, overlap = 94.6875
PHY-3002 : Step(80): len = 85808.6, overlap = 98.9062
PHY-3002 : Step(81): len = 84984.7, overlap = 99.25
PHY-3002 : Step(82): len = 83559.3, overlap = 99.625
PHY-3002 : Step(83): len = 82563.1, overlap = 99.6875
PHY-3002 : Step(84): len = 82098.4, overlap = 102
PHY-3002 : Step(85): len = 81576.3, overlap = 106.031
PHY-3002 : Step(86): len = 81433.6, overlap = 98.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.24986e-05
PHY-3002 : Step(87): len = 81775.4, overlap = 99.2188
PHY-3002 : Step(88): len = 81989.1, overlap = 98.8438
PHY-3002 : Step(89): len = 82464.2, overlap = 91.8438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000124997
PHY-3002 : Step(90): len = 82641.7, overlap = 87.3438
PHY-3002 : Step(91): len = 82760.3, overlap = 87.5
PHY-3002 : Step(92): len = 83322, overlap = 80.75
PHY-3002 : Step(93): len = 83656.7, overlap = 76.375
PHY-3002 : Step(94): len = 83745, overlap = 76.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012054s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (129.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.110246s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.32313e-05
PHY-3002 : Step(95): len = 91207.9, overlap = 58.0625
PHY-3002 : Step(96): len = 90098.8, overlap = 58.5625
PHY-3002 : Step(97): len = 89570.9, overlap = 54.25
PHY-3002 : Step(98): len = 89815.2, overlap = 57.9688
PHY-3002 : Step(99): len = 90001.5, overlap = 58.0938
PHY-3002 : Step(100): len = 89388.6, overlap = 58.875
PHY-3002 : Step(101): len = 88738.2, overlap = 60.7812
PHY-3002 : Step(102): len = 87900.9, overlap = 63.8125
PHY-3002 : Step(103): len = 87327.3, overlap = 64
PHY-3002 : Step(104): len = 86666.2, overlap = 64.25
PHY-3002 : Step(105): len = 86328.9, overlap = 66.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 2.64627e-05
PHY-3002 : Step(106): len = 86083.8, overlap = 65.6875
PHY-3002 : Step(107): len = 85878.8, overlap = 65.75
PHY-3002 : Step(108): len = 85839.1, overlap = 65.25
PHY-3002 : Step(109): len = 85872.3, overlap = 65.2812
PHY-3002 : Step(110): len = 85946.4, overlap = 66.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 5.29253e-05
PHY-3002 : Step(111): len = 85792.7, overlap = 65.375
PHY-3002 : Step(112): len = 85932.9, overlap = 62.4688
PHY-3002 : Step(113): len = 86026.8, overlap = 62.125
PHY-3002 : Step(114): len = 86052.1, overlap = 61.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.179663s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.35535e-05
PHY-3002 : Step(115): len = 86150.9, overlap = 114.875
PHY-3002 : Step(116): len = 86280.7, overlap = 114.625
PHY-3002 : Step(117): len = 86387.9, overlap = 110.312
PHY-3002 : Step(118): len = 87071.5, overlap = 103.969
PHY-3002 : Step(119): len = 87877.5, overlap = 103.094
PHY-3002 : Step(120): len = 87803.5, overlap = 102.094
PHY-3002 : Step(121): len = 88004.5, overlap = 100.219
PHY-3002 : Step(122): len = 88120.3, overlap = 100.094
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.7107e-05
PHY-3002 : Step(123): len = 88109.2, overlap = 99.0312
PHY-3002 : Step(124): len = 88326.7, overlap = 100.562
PHY-3002 : Step(125): len = 89125.6, overlap = 92.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000134214
PHY-3002 : Step(126): len = 89748.8, overlap = 85.1562
PHY-3002 : Step(127): len = 90838.9, overlap = 84.375
PHY-3002 : Step(128): len = 96162.8, overlap = 73.0312
PHY-3002 : Step(129): len = 96271.2, overlap = 67.75
PHY-3002 : Step(130): len = 95905.5, overlap = 67.9688
PHY-3002 : Step(131): len = 95739.2, overlap = 66.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000268428
PHY-3002 : Step(132): len = 95857.5, overlap = 65.5938
PHY-3002 : Step(133): len = 96432.2, overlap = 63.2188
PHY-3002 : Step(134): len = 98501.4, overlap = 55.9688
PHY-3002 : Step(135): len = 99417.3, overlap = 54.5938
PHY-3002 : Step(136): len = 98622.8, overlap = 53.9375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000536856
PHY-3002 : Step(137): len = 98935.9, overlap = 55.5625
PHY-3002 : Step(138): len = 99907.8, overlap = 53.8438
PHY-3002 : Step(139): len = 100145, overlap = 52.875
PHY-3002 : Step(140): len = 100013, overlap = 52.9062
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00107371
PHY-3002 : Step(141): len = 100619, overlap = 56.3438
PHY-3002 : Step(142): len = 101024, overlap = 54.8438
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00214742
PHY-3002 : Step(143): len = 101848, overlap = 53.875
PHY-3002 : Step(144): len = 101941, overlap = 54.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13969, tnet num: 3434, tinst num: 2756, tnode num: 19677, tedge num: 23533.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 144.12 peak overflow 4.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/3436.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 116688, over cnt = 475(1%), over = 1775, worst = 27
PHY-1001 : End global iterations;  0.202765s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.6%)

PHY-1001 : Congestion index: top1 = 57.07, top5 = 36.62, top10 = 27.52, top15 = 21.83.
PHY-1001 : End incremental global routing;  0.267236s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (111.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3434 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.134874s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (81.1%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2716 has valid locations, 36 needs to be replaced
PHY-3001 : design contains 2790 instances, 709 luts, 1632 seqs, 366 slices, 40 macros(366 instances: 245 mslices 121 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 102409
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 14105, tnet num: 3468, tinst num: 2790, tnode num: 19915, tedge num: 23737.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3468 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.478189s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(145): len = 102377, overlap = 0.78125
PHY-3002 : Step(146): len = 102482, overlap = 0.78125
PHY-3002 : Step(147): len = 102482, overlap = 0.78125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3468 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.106912s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000850147
PHY-3002 : Step(148): len = 102571, overlap = 54.8125
PHY-3002 : Step(149): len = 102571, overlap = 54.8125
PHY-3001 : Final: Len = 102571, Over = 54.8125
PHY-3001 : End incremental placement;  0.730196s wall, 0.734375s user + 0.078125s system = 0.812500s CPU (111.3%)

OPT-1001 : Total overflow 144.56 peak overflow 4.44
OPT-1001 : End high-fanout net optimization;  1.205164s wall, 1.328125s user + 0.078125s system = 1.406250s CPU (116.7%)

OPT-1001 : Current memory(MB): used = 276, reserve = 237, peak = 276.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2666/3470.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 118120, over cnt = 468(1%), over = 1755, worst = 27
PHY-1002 : len = 130792, over cnt = 319(0%), over = 693, worst = 15
PHY-1002 : len = 136952, over cnt = 105(0%), over = 161, worst = 6
PHY-1002 : len = 137696, over cnt = 39(0%), over = 54, worst = 6
PHY-1002 : len = 138456, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  0.260011s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (120.2%)

PHY-1001 : Congestion index: top1 = 48.43, top5 = 34.91, top10 = 28.01, top15 = 23.43.
OPT-1001 : End congestion update;  0.321162s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (111.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3468 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.117697s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (106.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.443259s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (109.3%)

OPT-1001 : Current memory(MB): used = 276, reserve = 237, peak = 277.
OPT-1001 : End physical optimization;  2.055390s wall, 2.234375s user + 0.109375s system = 2.343750s CPU (114.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 709 LUT to BLE ...
SYN-4008 : Packed 709 LUT and 248 SEQ to BLE.
SYN-4003 : Packing 1384 remaining SEQ's ...
SYN-4005 : Packed 417 SEQ with LUT/SLICE
SYN-4006 : 85 single LUT's are left
SYN-4006 : 967 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1676/2209 primitive instances ...
PHY-3001 : End packing;  0.106598s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (87.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1357 instances
RUN-1001 : 636 mslices, 636 lslices, 34 pads, 40 brams, 5 dsps
RUN-1001 : There are total 3241 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1933 nets have 2 pins
RUN-1001 : 1068 nets have [3 - 5] pins
RUN-1001 : 152 nets have [6 - 10] pins
RUN-1001 : 46 nets have [11 - 20] pins
RUN-1001 : 31 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 1355 instances, 1272 slices, 40 macros(366 instances: 245 mslices 121 lslices)
PHY-3001 : Cell area utilization is 16%
PHY-3001 : After packing: Len = 103773, Over = 90.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 16%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11992, tnet num: 3239, tinst num: 1355, tnode num: 16146, tedge num: 20748.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.478805s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.86467e-05
PHY-3002 : Step(150): len = 102652, overlap = 85.5
PHY-3002 : Step(151): len = 102694, overlap = 83.25
PHY-3002 : Step(152): len = 101799, overlap = 84.75
PHY-3002 : Step(153): len = 101400, overlap = 83.75
PHY-3002 : Step(154): len = 101110, overlap = 84.25
PHY-3002 : Step(155): len = 100778, overlap = 86
PHY-3002 : Step(156): len = 100577, overlap = 88.75
PHY-3002 : Step(157): len = 100551, overlap = 86
PHY-3002 : Step(158): len = 100637, overlap = 85.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.72934e-05
PHY-3002 : Step(159): len = 101552, overlap = 81.75
PHY-3002 : Step(160): len = 102268, overlap = 77.75
PHY-3002 : Step(161): len = 102973, overlap = 75.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000154587
PHY-3002 : Step(162): len = 105282, overlap = 65.75
PHY-3002 : Step(163): len = 106860, overlap = 67.75
PHY-3002 : Step(164): len = 106873, overlap = 67.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.183968s wall, 0.140625s user + 0.140625s system = 0.281250s CPU (152.9%)

PHY-3001 : Trial Legalized: Len = 124676
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 16%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.106123s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (88.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000587183
PHY-3002 : Step(165): len = 120022, overlap = 8.75
PHY-3002 : Step(166): len = 116438, overlap = 18
PHY-3002 : Step(167): len = 114068, overlap = 24.25
PHY-3002 : Step(168): len = 112898, overlap = 25.75
PHY-3002 : Step(169): len = 111890, overlap = 27
PHY-3002 : Step(170): len = 111168, overlap = 29.75
PHY-3002 : Step(171): len = 110663, overlap = 30.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117437
PHY-3002 : Step(172): len = 111036, overlap = 31.5
PHY-3002 : Step(173): len = 111480, overlap = 29.5
PHY-3002 : Step(174): len = 111400, overlap = 30.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00234873
PHY-3002 : Step(175): len = 111669, overlap = 30
PHY-3002 : Step(176): len = 111935, overlap = 28.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005366s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 119180, Over = 0
PHY-3001 : Spreading special nets. 43 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.012706s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.0%)

PHY-3001 : 62 instances has been re-located, deltaX = 35, deltaY = 34, maxDist = 2.
PHY-3001 : Final: Len = 120190, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11992, tnet num: 3239, tinst num: 1355, tnode num: 16146, tedge num: 20748.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 118/3241.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 140016, over cnt = 363(1%), over = 596, worst = 8
PHY-1002 : len = 141616, over cnt = 229(0%), over = 327, worst = 6
PHY-1002 : len = 144416, over cnt = 55(0%), over = 64, worst = 3
PHY-1002 : len = 145280, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 145320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.336794s wall, 0.468750s user + 0.046875s system = 0.515625s CPU (153.1%)

PHY-1001 : Congestion index: top1 = 39.25, top5 = 31.55, top10 = 26.57, top15 = 23.14.
PHY-1001 : End incremental global routing;  0.411661s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (144.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.122770s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (89.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.590762s wall, 0.718750s user + 0.046875s system = 0.765625s CPU (129.6%)

OPT-1001 : Current memory(MB): used = 273, reserve = 234, peak = 277.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2852/3241.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 145320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.014261s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 39.25, top5 = 31.55, top10 = 26.57, top15 = 23.14.
OPT-1001 : End congestion update;  0.078007s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.086516s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.171049s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.5%)

OPT-1001 : Current memory(MB): used = 277, reserve = 237, peak = 277.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.096558s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2852/3241.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 145320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.014297s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.3%)

PHY-1001 : Congestion index: top1 = 39.25, top5 = 31.55, top10 = 26.57, top15 = 23.14.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.091505s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 38.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.447867s wall, 1.562500s user + 0.046875s system = 1.609375s CPU (111.2%)

RUN-1003 : finish command "place" in  9.263727s wall, 14.453125s user + 3.250000s system = 17.703125s CPU (191.1%)

RUN-1004 : used memory is 277 MB, reserved memory is 238 MB, peak memory is 277 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1357 instances
RUN-1001 : 636 mslices, 636 lslices, 34 pads, 40 brams, 5 dsps
RUN-1001 : There are total 3241 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1933 nets have 2 pins
RUN-1001 : 1068 nets have [3 - 5] pins
RUN-1001 : 152 nets have [6 - 10] pins
RUN-1001 : 46 nets have [11 - 20] pins
RUN-1001 : 31 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11992, tnet num: 3239, tinst num: 1355, tnode num: 16146, tedge num: 20748.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 636 mslices, 636 lslices, 34 pads, 40 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 139216, over cnt = 377(1%), over = 625, worst = 8
PHY-1002 : len = 141000, over cnt = 209(0%), over = 312, worst = 8
PHY-1002 : len = 143896, over cnt = 47(0%), over = 75, worst = 7
PHY-1002 : len = 145000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.312066s wall, 0.375000s user + 0.031250s system = 0.406250s CPU (130.2%)

PHY-1001 : Congestion index: top1 = 39.09, top5 = 31.40, top10 = 26.47, top15 = 23.01.
PHY-1001 : End global routing;  0.387790s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (124.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 291, reserve = 251, peak = 325.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net RxTransmit_dup_1 will be routed on clock mesh
PHY-1001 : net TxTransmit_dup_1 will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 553, reserve = 516, peak = 553.
PHY-1001 : End build detailed router design. 3.761675s wall, 3.656250s user + 0.078125s system = 3.734375s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 48912, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.433579s wall, 1.359375s user + 0.015625s system = 1.375000s CPU (95.9%)

PHY-1001 : Current memory(MB): used = 585, reserve = 549, peak = 585.
PHY-1001 : End phase 1; 1.440006s wall, 1.375000s user + 0.015625s system = 1.390625s CPU (96.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 396528, over cnt = 55(0%), over = 55, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 587, reserve = 551, peak = 588.
PHY-1001 : End initial routed; 3.789022s wall, 5.171875s user + 0.234375s system = 5.406250s CPU (142.7%)

PHY-1001 : Update timing.....
PHY-1001 : 3/2878(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.096   |  -0.096   |   1   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.593601s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 589, reserve = 553, peak = 589.
PHY-1001 : End phase 2; 4.382706s wall, 5.765625s user + 0.234375s system = 6.000000s CPU (136.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.046ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.025385s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.6%)

PHY-1022 : len = 396544, over cnt = 56(0%), over = 56, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.053737s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 394536, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.091585s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 394616, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.042143s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (111.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 394616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.030651s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2878(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.046   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.606927s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (97.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.414397s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 609, reserve = 573, peak = 609.
PHY-1001 : End phase 3; 1.399427s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 394616
PHY-1001 : Current memory(MB): used = 609, reserve = 574, peak = 609.
PHY-1001 : End export database. 0.014955s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.5%)

PHY-1001 : End detail routing;  11.211774s wall, 12.421875s user + 0.343750s system = 12.765625s CPU (113.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11992, tnet num: 3239, tinst num: 1355, tnode num: 16146, tedge num: 20748.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  12.807410s wall, 14.078125s user + 0.375000s system = 14.453125s CPU (112.8%)

RUN-1004 : used memory is 609 MB, reserved memory is 574 MB, peak memory is 609 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1446   out of  19600    7.38%
#reg                     1714   out of  19600    8.74%
#le                      2413
  #lut only               699   out of   2413   28.97%
  #reg only               967   out of   2413   40.07%
  #lut&reg                747   out of   2413   30.96%
#dsp                        5   out of     29   17.24%
#bram                      40   out of     64   62.50%
  #bram9k                  40
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    17
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                                      Fanout
#1        TxTransmit_dup_1     GCLK               pll                CLK120/pll_inst.clkc0                       629
#2        config_inst_syn_9    GCLK               config             config_inst.jtck                            223
#3        RxTransmit_dup_1     GCLK               pll                CLK120/pll_inst.clkc1                       124
#4        wendu/clk_us         GCLK               lslice             signal_process/trans/clk_out_n_syn_35.q0    41
#5        clk_in_dup_1         GCLK               io                 clk_in_syn_2.di                             15


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       NONE    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |2413   |1080    |366     |1744    |40      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1169   |340     |146     |954     |7       |5       |
|    ctrl_signal                     |SignalGenerator                                  |33     |24      |9       |23      |0       |0       |
|    demodu                          |Demodulation                                     |577    |136     |68      |464     |7       |0       |
|      fifo                          |Asys_fifo56X16                                   |196    |73      |30      |160     |7       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |55     |1       |0       |55      |7       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |34     |15      |0       |34      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |40     |24      |0       |40      |0       |0       |
|    integ                           |Integration                                      |136    |14      |14      |110     |0       |0       |
|    modu                            |Modulation                                       |87     |22      |21      |83      |0       |1       |
|    rs422                           |Rs422Output                                      |315    |128     |29      |256     |0       |4       |
|    trans                           |SquareWaveGenerator                              |21     |16      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |77     |62      |7       |51      |0       |0       |
|    U0                              |speed_select_Tx                                  |33     |24      |7       |19      |0       |0       |
|    U1                              |uart_tx                                          |15     |9       |0       |15      |0       |0       |
|    U2                              |Ctrl_Data                                        |29     |29      |0       |17      |0       |0       |
|  wendu                             |DS18B20                                          |208    |163     |45      |76      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |860    |476     |131     |603     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |860    |476     |131     |603     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |400    |183     |0       |400     |0       |0       |
|        reg_inst                    |register                                         |397    |180     |0       |397     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |460    |293     |131     |203     |0       |0       |
|        bus_inst                    |bus_top                                          |233    |155     |74      |90      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |49     |33      |16      |18      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |178    |116     |58      |66      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det                                          |4      |4       |0       |4       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |146    |95      |29      |82      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1899  
    #2          2       736   
    #3          3       247   
    #4          4        85   
    #5        5-10      155   
    #6        11-50      72   
    #7       101-500     5    
  Average     2.40            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11992, tnet num: 3239, tinst num: 1355, tnode num: 16146, tedge num: 20748.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 3239 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 4. Number of clock nets = 5 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9e202d84abd741035cca8c654a52b393192d5581719867dd9b240f37c1176529 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1355
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 3241, pip num: 28779
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1906 valid insts, and 73835 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101100010011010101101111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.182270s wall, 31.484375s user + 0.187500s system = 31.671875s CPU (611.2%)

RUN-1004 : used memory is 619 MB, reserved memory is 582 MB, peak memory is 752 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230714_185414.log"
