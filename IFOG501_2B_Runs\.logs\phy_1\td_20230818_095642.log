============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Aug 18 09:56:43 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 380 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2183 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1648 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 380 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7713, tnet num: 2181, tinst num: 1625, tnode num: 10898, tedge num: 12989.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.263288s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542523
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 448383, overlap = 18
PHY-3002 : Step(2): len = 345957, overlap = 15.75
PHY-3002 : Step(3): len = 325937, overlap = 15.75
PHY-3002 : Step(4): len = 314137, overlap = 20.25
PHY-3002 : Step(5): len = 307245, overlap = 18
PHY-3002 : Step(6): len = 302674, overlap = 20.25
PHY-3002 : Step(7): len = 296218, overlap = 20.25
PHY-3002 : Step(8): len = 289810, overlap = 15.75
PHY-3002 : Step(9): len = 282463, overlap = 15.75
PHY-3002 : Step(10): len = 279015, overlap = 15.75
PHY-3002 : Step(11): len = 268286, overlap = 13.5
PHY-3002 : Step(12): len = 263653, overlap = 15.75
PHY-3002 : Step(13): len = 259135, overlap = 18
PHY-3002 : Step(14): len = 253572, overlap = 18
PHY-3002 : Step(15): len = 245912, overlap = 18
PHY-3002 : Step(16): len = 242676, overlap = 18
PHY-3002 : Step(17): len = 236773, overlap = 18
PHY-3002 : Step(18): len = 231237, overlap = 18
PHY-3002 : Step(19): len = 226071, overlap = 18
PHY-3002 : Step(20): len = 223047, overlap = 18
PHY-3002 : Step(21): len = 212062, overlap = 15.75
PHY-3002 : Step(22): len = 206619, overlap = 18
PHY-3002 : Step(23): len = 203962, overlap = 18
PHY-3002 : Step(24): len = 197994, overlap = 18
PHY-3002 : Step(25): len = 167421, overlap = 18
PHY-3002 : Step(26): len = 163212, overlap = 20.25
PHY-3002 : Step(27): len = 161566, overlap = 20.25
PHY-3002 : Step(28): len = 141461, overlap = 15.75
PHY-3002 : Step(29): len = 128769, overlap = 18
PHY-3002 : Step(30): len = 126709, overlap = 15.75
PHY-3002 : Step(31): len = 123018, overlap = 15.75
PHY-3002 : Step(32): len = 121063, overlap = 15.75
PHY-3002 : Step(33): len = 117702, overlap = 20.25
PHY-3002 : Step(34): len = 114156, overlap = 15.75
PHY-3002 : Step(35): len = 112185, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000137657
PHY-3002 : Step(36): len = 112204, overlap = 6.75
PHY-3002 : Step(37): len = 111739, overlap = 9
PHY-3002 : Step(38): len = 111020, overlap = 11.25
PHY-3002 : Step(39): len = 110226, overlap = 9
PHY-3002 : Step(40): len = 107925, overlap = 11.25
PHY-3002 : Step(41): len = 105891, overlap = 9
PHY-3002 : Step(42): len = 103337, overlap = 6.75
PHY-3002 : Step(43): len = 102956, overlap = 6.75
PHY-3002 : Step(44): len = 100925, overlap = 9
PHY-3002 : Step(45): len = 98607, overlap = 9
PHY-3002 : Step(46): len = 97193, overlap = 9
PHY-3002 : Step(47): len = 95910.6, overlap = 11.25
PHY-3002 : Step(48): len = 94498, overlap = 9
PHY-3002 : Step(49): len = 93364.8, overlap = 9
PHY-3002 : Step(50): len = 91813.4, overlap = 6.75
PHY-3002 : Step(51): len = 88945.5, overlap = 9
PHY-3002 : Step(52): len = 86782.1, overlap = 9.0625
PHY-3002 : Step(53): len = 85768, overlap = 11.375
PHY-3002 : Step(54): len = 84288.8, overlap = 11.3125
PHY-3002 : Step(55): len = 81657.3, overlap = 11.3125
PHY-3002 : Step(56): len = 80006.4, overlap = 11.25
PHY-3002 : Step(57): len = 78659.8, overlap = 11.25
PHY-3002 : Step(58): len = 77250.7, overlap = 9.25
PHY-3002 : Step(59): len = 76633.4, overlap = 6.9375
PHY-3002 : Step(60): len = 74473.2, overlap = 7.4375
PHY-3002 : Step(61): len = 73694.8, overlap = 9.5
PHY-3002 : Step(62): len = 71509, overlap = 14.3125
PHY-3002 : Step(63): len = 70369.8, overlap = 12.0625
PHY-3002 : Step(64): len = 69375.6, overlap = 7.5625
PHY-3002 : Step(65): len = 68492.8, overlap = 7.625
PHY-3002 : Step(66): len = 67406.3, overlap = 14
PHY-3002 : Step(67): len = 64553.5, overlap = 13.875
PHY-3002 : Step(68): len = 63599.6, overlap = 7
PHY-3002 : Step(69): len = 63563.3, overlap = 9.1875
PHY-3002 : Step(70): len = 63263.3, overlap = 9.3125
PHY-3002 : Step(71): len = 62788.3, overlap = 11.5
PHY-3002 : Step(72): len = 62252.4, overlap = 11.5625
PHY-3002 : Step(73): len = 61780.8, overlap = 11.5625
PHY-3002 : Step(74): len = 61810.6, overlap = 16.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000275314
PHY-3002 : Step(75): len = 61911, overlap = 9.375
PHY-3002 : Step(76): len = 61836.8, overlap = 7.125
PHY-3002 : Step(77): len = 61816.7, overlap = 7.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000550629
PHY-3002 : Step(78): len = 61828.8, overlap = 9.3125
PHY-3002 : Step(79): len = 61803.8, overlap = 9.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006713s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (232.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061257s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 64870.2, overlap = 6.875
PHY-3002 : Step(81): len = 63548.3, overlap = 6.3125
PHY-3002 : Step(82): len = 61769.5, overlap = 5.6875
PHY-3002 : Step(83): len = 60288.2, overlap = 4.625
PHY-3002 : Step(84): len = 58781.6, overlap = 4.25
PHY-3002 : Step(85): len = 57800.6, overlap = 4.5625
PHY-3002 : Step(86): len = 56715.2, overlap = 5.1875
PHY-3002 : Step(87): len = 55382.5, overlap = 5.9375
PHY-3002 : Step(88): len = 53649.3, overlap = 6.25
PHY-3002 : Step(89): len = 53110.6, overlap = 6.375
PHY-3002 : Step(90): len = 52777.7, overlap = 6.75
PHY-3002 : Step(91): len = 52027.8, overlap = 6.375
PHY-3002 : Step(92): len = 51472, overlap = 6.1875
PHY-3002 : Step(93): len = 50667.4, overlap = 6.1875
PHY-3002 : Step(94): len = 50008, overlap = 7.625
PHY-3002 : Step(95): len = 49762.9, overlap = 7.5
PHY-3002 : Step(96): len = 49492.9, overlap = 7.5
PHY-3002 : Step(97): len = 49583.6, overlap = 7.5
PHY-3002 : Step(98): len = 49616.6, overlap = 7.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0062995
PHY-3002 : Step(99): len = 49040.6, overlap = 7.4375
PHY-3002 : Step(100): len = 48819.4, overlap = 7.375
PHY-3002 : Step(101): len = 48792.7, overlap = 7.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069223s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000147315
PHY-3002 : Step(102): len = 49473.9, overlap = 51.2188
PHY-3002 : Step(103): len = 50108.2, overlap = 49.625
PHY-3002 : Step(104): len = 50234.1, overlap = 46.4375
PHY-3002 : Step(105): len = 50184.3, overlap = 39.6562
PHY-3002 : Step(106): len = 50051, overlap = 38.4375
PHY-3002 : Step(107): len = 50146.4, overlap = 37.9062
PHY-3002 : Step(108): len = 50073.4, overlap = 40.0312
PHY-3002 : Step(109): len = 49945.6, overlap = 37.6562
PHY-3002 : Step(110): len = 49757.5, overlap = 35.7188
PHY-3002 : Step(111): len = 49458.8, overlap = 37.5312
PHY-3002 : Step(112): len = 49100.5, overlap = 37.5938
PHY-3002 : Step(113): len = 48930.1, overlap = 38.75
PHY-3002 : Step(114): len = 48743.6, overlap = 36.4688
PHY-3002 : Step(115): len = 48753.1, overlap = 36.9688
PHY-3002 : Step(116): len = 48597.6, overlap = 36.9375
PHY-3002 : Step(117): len = 48204.1, overlap = 36.25
PHY-3002 : Step(118): len = 48021.8, overlap = 40.125
PHY-3002 : Step(119): len = 47812.8, overlap = 40.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00029463
PHY-3002 : Step(120): len = 47756.5, overlap = 41.1875
PHY-3002 : Step(121): len = 47997.1, overlap = 37.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000589261
PHY-3002 : Step(122): len = 48234.1, overlap = 35.3438
PHY-3002 : Step(123): len = 48559.2, overlap = 35.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7713, tnet num: 2181, tinst num: 1625, tnode num: 10898, tedge num: 12989.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.59 peak overflow 2.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51904, over cnt = 243(0%), over = 989, worst = 16
PHY-1001 : End global iterations;  0.071386s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.6%)

PHY-1001 : Congestion index: top1 = 40.22, top5 = 24.06, top10 = 15.24, top15 = 10.82.
PHY-1001 : End incremental global routing;  0.119935s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064242s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (97.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.213537s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (102.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1623/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51904, over cnt = 243(0%), over = 989, worst = 16
PHY-1002 : len = 58584, over cnt = 157(0%), over = 289, worst = 6
PHY-1002 : len = 60192, over cnt = 82(0%), over = 106, worst = 5
PHY-1002 : len = 61440, over cnt = 23(0%), over = 24, worst = 2
PHY-1002 : len = 61832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088912s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.4%)

PHY-1001 : Congestion index: top1 = 35.88, top5 = 23.89, top10 = 16.66, top15 = 12.26.
OPT-1001 : End congestion update;  0.129316s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055040s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.186864s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.662188s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (103.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1078/1404 primitive instances ...
PHY-3001 : End packing;  0.049082s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 832 instances
RUN-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1480 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 830 instances, 783 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48902.6, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 830, tnode num: 8772, tedge num: 11353.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.298867s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (104.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.80183e-05
PHY-3002 : Step(124): len = 48373.2, overlap = 60.25
PHY-3002 : Step(125): len = 48104.1, overlap = 59.5
PHY-3002 : Step(126): len = 47964.1, overlap = 59.5
PHY-3002 : Step(127): len = 48145.4, overlap = 62.5
PHY-3002 : Step(128): len = 48267.4, overlap = 62.75
PHY-3002 : Step(129): len = 48101, overlap = 63.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.60365e-05
PHY-3002 : Step(130): len = 48472.7, overlap = 61.5
PHY-3002 : Step(131): len = 48771.8, overlap = 62
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112073
PHY-3002 : Step(132): len = 49205, overlap = 60
PHY-3002 : Step(133): len = 50716.8, overlap = 55
PHY-3002 : Step(134): len = 51754.3, overlap = 52
PHY-3002 : Step(135): len = 51706.6, overlap = 51.5
PHY-3002 : Step(136): len = 51686.3, overlap = 51
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078092s wall, 0.078125s user + 0.109375s system = 0.187500s CPU (240.1%)

PHY-3001 : Trial Legalized: Len = 64011
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051508s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000672737
PHY-3002 : Step(137): len = 61836.9, overlap = 5.25
PHY-3002 : Step(138): len = 59563.6, overlap = 11
PHY-3002 : Step(139): len = 57749.5, overlap = 17
PHY-3002 : Step(140): len = 56983.2, overlap = 16.75
PHY-3002 : Step(141): len = 56469.7, overlap = 18.75
PHY-3002 : Step(142): len = 56060.9, overlap = 19
PHY-3002 : Step(143): len = 55819.3, overlap = 19.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00134547
PHY-3002 : Step(144): len = 56238.1, overlap = 20
PHY-3002 : Step(145): len = 56297.9, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00269095
PHY-3002 : Step(146): len = 56454.1, overlap = 19.25
PHY-3002 : Step(147): len = 56534.9, overlap = 19.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004714s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61165.9, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005613s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 4, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 61433.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 830, tnode num: 8772, tedge num: 11353.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 140/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68296, over cnt = 162(0%), over = 242, worst = 7
PHY-1002 : len = 69448, over cnt = 66(0%), over = 73, worst = 3
PHY-1002 : len = 70400, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 70480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131297s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (107.1%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.38, top10 = 17.40, top15 = 13.69.
PHY-1001 : End incremental global routing;  0.180949s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057292s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.267071s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (105.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006084s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.38, top10 = 17.40, top15 = 13.69.
OPT-1001 : End congestion update;  0.050629s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048344s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.100635s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050121s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005428s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.38, top10 = 17.40, top15 = 13.69.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048368s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.820005s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (101.0%)

RUN-1003 : finish command "place" in  5.048773s wall, 6.656250s user + 2.406250s system = 9.062500s CPU (179.5%)

RUN-1004 : used memory is 200 MB, reserved memory is 163 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 832 instances
RUN-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1480 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 830, tnode num: 8772, tedge num: 11353.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67216, over cnt = 157(0%), over = 239, worst = 7
PHY-1002 : len = 68168, over cnt = 106(0%), over = 136, worst = 3
PHY-1002 : len = 69888, over cnt = 3(0%), over = 6, worst = 3
PHY-1002 : len = 69984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133728s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 30.54, top5 = 22.20, top10 = 17.22, top15 = 13.59.
PHY-1001 : End global routing;  0.181315s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 466, peak = 500.
PHY-1001 : End build detailed router design. 5.623934s wall, 5.609375s user + 0.015625s system = 5.625000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.420298s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 532, reserve = 499, peak = 532.
PHY-1001 : End phase 1; 2.432235s wall, 2.437500s user + 0.000000s system = 2.437500s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176848, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End initial routed; 1.826262s wall, 3.078125s user + 0.296875s system = 3.375000s CPU (184.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1778(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.295   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.663926s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 2.490465s wall, 3.734375s user + 0.296875s system = 4.031250s CPU (161.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176848, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.036638s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176856, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.065244s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176888, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.052804s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1778(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.295   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.717105s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.471929s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End phase 3; 1.607143s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 176888
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.014989s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.2%)

PHY-1001 : End detail routing;  12.514832s wall, 13.718750s user + 0.328125s system = 14.046875s CPU (112.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 830, tnode num: 8772, tedge num: 11353.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  13.813266s wall, 15.000000s user + 0.359375s system = 15.359375s CPU (111.2%)

RUN-1004 : used memory is 502 MB, reserved memory is 474 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      824   out of  19600    4.20%
#reg                     1047   out of  19600    5.34%
#le                      1522
  #lut only               475   out of   1522   31.21%
  #reg only               698   out of   1522   45.86%
  #lut&reg                349   out of   1522   22.93%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         456
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1522   |605     |219     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1118   |298     |126     |894     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |25      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |539    |117     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |2       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |14      |0       |28      |0       |0       |
|    integ                   |Integration                                      |141    |16      |14      |115     |0       |0       |
|    modu                    |Modulation                                       |60     |26      |14      |56      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |89      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |101     |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |19      |0       |0       |
|    U1                      |uart_tx                                          |20     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |55      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1444  
    #2          2       287   
    #3          3       110   
    #4          4        14   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 830, tnode num: 8772, tedge num: 11353.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 830
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14453
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38455 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.650432s wall, 19.515625s user + 0.156250s system = 19.671875s CPU (538.9%)

RUN-1004 : used memory is 517 MB, reserved memory is 485 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230818_095642.log"
