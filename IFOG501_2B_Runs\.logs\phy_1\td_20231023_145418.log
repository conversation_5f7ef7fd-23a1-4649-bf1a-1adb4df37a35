============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Oct 23 14:54:18 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1542 instances
RUN-0007 : 373 luts, 908 seqs, 137 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2096 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1554 nets have 2 pins
RUN-1001 : 426 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1540 instances, 373 luts, 908 seqs, 212 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7393, tnet num: 2094, tinst num: 1540, tnode num: 10377, tedge num: 12540.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.355537s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 539328
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1540.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 459450, overlap = 13.5
PHY-3002 : Step(2): len = 410668, overlap = 18
PHY-3002 : Step(3): len = 385324, overlap = 18
PHY-3002 : Step(4): len = 362316, overlap = 15.75
PHY-3002 : Step(5): len = 355302, overlap = 18
PHY-3002 : Step(6): len = 350313, overlap = 20.25
PHY-3002 : Step(7): len = 336674, overlap = 18
PHY-3002 : Step(8): len = 319927, overlap = 20.25
PHY-3002 : Step(9): len = 315418, overlap = 18
PHY-3002 : Step(10): len = 308343, overlap = 15.75
PHY-3002 : Step(11): len = 298687, overlap = 18
PHY-3002 : Step(12): len = 292195, overlap = 13.5
PHY-3002 : Step(13): len = 287575, overlap = 18
PHY-3002 : Step(14): len = 278262, overlap = 15.75
PHY-3002 : Step(15): len = 272546, overlap = 18
PHY-3002 : Step(16): len = 267962, overlap = 13.5
PHY-3002 : Step(17): len = 262397, overlap = 13.5
PHY-3002 : Step(18): len = 256556, overlap = 11.25
PHY-3002 : Step(19): len = 252573, overlap = 13.5
PHY-3002 : Step(20): len = 245984, overlap = 11.25
PHY-3002 : Step(21): len = 241556, overlap = 13.5
PHY-3002 : Step(22): len = 236325, overlap = 11.25
PHY-3002 : Step(23): len = 232222, overlap = 13.5
PHY-3002 : Step(24): len = 225493, overlap = 11.25
PHY-3002 : Step(25): len = 221839, overlap = 13.5
PHY-3002 : Step(26): len = 217381, overlap = 11.25
PHY-3002 : Step(27): len = 209941, overlap = 13.5
PHY-3002 : Step(28): len = 202436, overlap = 11.25
PHY-3002 : Step(29): len = 200306, overlap = 13.5
PHY-3002 : Step(30): len = 192041, overlap = 11.25
PHY-3002 : Step(31): len = 181350, overlap = 13.5
PHY-3002 : Step(32): len = 176702, overlap = 11.25
PHY-3002 : Step(33): len = 174586, overlap = 13.5
PHY-3002 : Step(34): len = 156863, overlap = 13.5
PHY-3002 : Step(35): len = 144505, overlap = 15.75
PHY-3002 : Step(36): len = 142394, overlap = 15.75
PHY-3002 : Step(37): len = 134462, overlap = 15.75
PHY-3002 : Step(38): len = 104617, overlap = 13.5
PHY-3002 : Step(39): len = 102953, overlap = 13.5
PHY-3002 : Step(40): len = 101287, overlap = 13.5
PHY-3002 : Step(41): len = 97305.9, overlap = 13.5
PHY-3002 : Step(42): len = 95291.5, overlap = 13.5
PHY-3002 : Step(43): len = 93658.7, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.84812e-05
PHY-3002 : Step(44): len = 93568.6, overlap = 13.5
PHY-3002 : Step(45): len = 93674.3, overlap = 15.75
PHY-3002 : Step(46): len = 93368, overlap = 13.5
PHY-3002 : Step(47): len = 92520.3, overlap = 13.5
PHY-3002 : Step(48): len = 91708.2, overlap = 13.5
PHY-3002 : Step(49): len = 87601.4, overlap = 13.5
PHY-3002 : Step(50): len = 84860.1, overlap = 13.5
PHY-3002 : Step(51): len = 83875.5, overlap = 13.5
PHY-3002 : Step(52): len = 82427.6, overlap = 11.25
PHY-3002 : Step(53): len = 79796.3, overlap = 13.5
PHY-3002 : Step(54): len = 78749.3, overlap = 13.5
PHY-3002 : Step(55): len = 77782.8, overlap = 13.5
PHY-3002 : Step(56): len = 75163.6, overlap = 15.75
PHY-3002 : Step(57): len = 74008.6, overlap = 13.5
PHY-3002 : Step(58): len = 73107.7, overlap = 13.5
PHY-3002 : Step(59): len = 70547.5, overlap = 11.875
PHY-3002 : Step(60): len = 69151.5, overlap = 12.125
PHY-3002 : Step(61): len = 68039.7, overlap = 12.5
PHY-3002 : Step(62): len = 67059, overlap = 12.625
PHY-3002 : Step(63): len = 66411.7, overlap = 15.1875
PHY-3002 : Step(64): len = 65255.4, overlap = 15.375
PHY-3002 : Step(65): len = 62971.8, overlap = 15.6875
PHY-3002 : Step(66): len = 60854.5, overlap = 16.25
PHY-3002 : Step(67): len = 59967.9, overlap = 16.0625
PHY-3002 : Step(68): len = 59069.2, overlap = 13.75
PHY-3002 : Step(69): len = 58865.9, overlap = 11.5625
PHY-3002 : Step(70): len = 58550.5, overlap = 11.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000176962
PHY-3002 : Step(71): len = 58820, overlap = 11.6875
PHY-3002 : Step(72): len = 58934.5, overlap = 13.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000353925
PHY-3002 : Step(73): len = 58734.9, overlap = 13.9375
PHY-3002 : Step(74): len = 58760, overlap = 13.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006637s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069662s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000376303
PHY-3002 : Step(75): len = 61373.2, overlap = 18.5625
PHY-3002 : Step(76): len = 60892.3, overlap = 18.1875
PHY-3002 : Step(77): len = 59157.8, overlap = 17.2812
PHY-3002 : Step(78): len = 58381.9, overlap = 17.7188
PHY-3002 : Step(79): len = 57144.6, overlap = 17.2812
PHY-3002 : Step(80): len = 56323.8, overlap = 14.2188
PHY-3002 : Step(81): len = 55890.4, overlap = 17.2188
PHY-3002 : Step(82): len = 55310.7, overlap = 17.3125
PHY-3002 : Step(83): len = 54671.6, overlap = 17.125
PHY-3002 : Step(84): len = 54509.9, overlap = 17.1562
PHY-3002 : Step(85): len = 53493.4, overlap = 21.1562
PHY-3002 : Step(86): len = 52775.7, overlap = 21.5625
PHY-3002 : Step(87): len = 52294, overlap = 21.4375
PHY-3002 : Step(88): len = 51890.6, overlap = 21.6875
PHY-3002 : Step(89): len = 51561.8, overlap = 21.5312
PHY-3002 : Step(90): len = 51349.2, overlap = 21.1875
PHY-3002 : Step(91): len = 51313.1, overlap = 19.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000752607
PHY-3002 : Step(92): len = 50841.2, overlap = 19.75
PHY-3002 : Step(93): len = 50589.2, overlap = 19.75
PHY-3002 : Step(94): len = 50559.8, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00150521
PHY-3002 : Step(95): len = 50357.5, overlap = 19.875
PHY-3002 : Step(96): len = 50448.9, overlap = 19.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065538s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.27817e-05
PHY-3002 : Step(97): len = 50615.7, overlap = 58.2812
PHY-3002 : Step(98): len = 51855.1, overlap = 50.75
PHY-3002 : Step(99): len = 52196.1, overlap = 48.3438
PHY-3002 : Step(100): len = 51985, overlap = 48.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000125563
PHY-3002 : Step(101): len = 51886.4, overlap = 48.6875
PHY-3002 : Step(102): len = 52028.9, overlap = 43.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000251127
PHY-3002 : Step(103): len = 52270.6, overlap = 47.0625
PHY-3002 : Step(104): len = 54853.7, overlap = 38.8125
PHY-3002 : Step(105): len = 55189.8, overlap = 38.9688
PHY-3002 : Step(106): len = 54816.6, overlap = 35.5
PHY-3002 : Step(107): len = 54551.3, overlap = 32.5938
PHY-3002 : Step(108): len = 54138.1, overlap = 29.8125
PHY-3002 : Step(109): len = 54148.4, overlap = 29.375
PHY-3002 : Step(110): len = 54313.2, overlap = 29.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7393, tnet num: 2094, tinst num: 1540, tnode num: 10377, tedge num: 12540.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 72.00 peak overflow 2.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2096.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58288, over cnt = 251(0%), over = 1011, worst = 20
PHY-1001 : End global iterations;  0.100158s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (171.6%)

PHY-1001 : Congestion index: top1 = 46.21, top5 = 27.17, top10 = 16.91, top15 = 11.89.
PHY-1001 : End incremental global routing;  0.158469s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (147.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080137s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (97.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273898s wall, 0.312500s user + 0.046875s system = 0.359375s CPU (131.2%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1621/2096.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58288, over cnt = 251(0%), over = 1011, worst = 20
PHY-1002 : len = 65112, over cnt = 161(0%), over = 393, worst = 13
PHY-1002 : len = 68464, over cnt = 39(0%), over = 87, worst = 13
PHY-1002 : len = 68896, over cnt = 20(0%), over = 23, worst = 3
PHY-1002 : len = 69312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112112s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (167.2%)

PHY-1001 : Congestion index: top1 = 38.94, top5 = 26.23, top10 = 18.54, top15 = 13.47.
OPT-1001 : End congestion update;  0.160225s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (136.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2094 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062507s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.226501s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (124.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : End physical optimization;  0.813445s wall, 0.859375s user + 0.093750s system = 0.953125s CPU (117.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 172 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 126 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 610 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 983/1278 primitive instances ...
PHY-3001 : End packing;  0.053892s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 783 instances
RUN-1001 : 367 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1391 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 781 instances, 734 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54424.2, Over = 53.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6243, tnet num: 1930, tinst num: 781, tnode num: 8405, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.374530s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.55523e-05
PHY-3002 : Step(111): len = 53831.7, overlap = 55.5
PHY-3002 : Step(112): len = 53105.3, overlap = 54
PHY-3002 : Step(113): len = 52862.9, overlap = 55
PHY-3002 : Step(114): len = 52837.3, overlap = 55.25
PHY-3002 : Step(115): len = 52944.1, overlap = 57.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.11047e-05
PHY-3002 : Step(116): len = 53246.7, overlap = 56.75
PHY-3002 : Step(117): len = 53816.1, overlap = 52.25
PHY-3002 : Step(118): len = 54342.9, overlap = 51.5
PHY-3002 : Step(119): len = 54417.4, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000102209
PHY-3002 : Step(120): len = 55326.5, overlap = 47.75
PHY-3002 : Step(121): len = 56536, overlap = 48.75
PHY-3002 : Step(122): len = 56746.5, overlap = 46.5
PHY-3002 : Step(123): len = 56618.4, overlap = 44.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097247s wall, 0.046875s user + 0.171875s system = 0.218750s CPU (224.9%)

PHY-3001 : Trial Legalized: Len = 70121.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054503s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000764795
PHY-3002 : Step(124): len = 67453.8, overlap = 6.25
PHY-3002 : Step(125): len = 65222.2, overlap = 9
PHY-3002 : Step(126): len = 63955.3, overlap = 11
PHY-3002 : Step(127): len = 62744.1, overlap = 15.5
PHY-3002 : Step(128): len = 62052.3, overlap = 17.75
PHY-3002 : Step(129): len = 61834, overlap = 17.5
PHY-3002 : Step(130): len = 61795.7, overlap = 17.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00152959
PHY-3002 : Step(131): len = 61923, overlap = 16.75
PHY-3002 : Step(132): len = 62065.9, overlap = 17
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00305918
PHY-3002 : Step(133): len = 62169.3, overlap = 17.5
PHY-3002 : Step(134): len = 62169.3, overlap = 17.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006455s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (242.1%)

PHY-3001 : Legalized: Len = 66249.3, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006452s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 2, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 66325.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6243, tnet num: 1930, tinst num: 781, tnode num: 8405, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 41/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72096, over cnt = 126(0%), over = 172, worst = 5
PHY-1002 : len = 72616, over cnt = 52(0%), over = 63, worst = 4
PHY-1002 : len = 73392, over cnt = 7(0%), over = 9, worst = 2
PHY-1002 : len = 73520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152984s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.1%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.53, top10 = 17.72, top15 = 13.93.
PHY-1001 : End incremental global routing;  0.214759s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070239s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.317004s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.6%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005923s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.53, top10 = 17.72, top15 = 13.93.
OPT-1001 : End congestion update;  0.056352s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (110.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053056s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 743 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 781 instances, 734 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 66341.2, Over = 0
PHY-3001 : End spreading;  0.004876s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 66341.2, Over = 0
PHY-3001 : End incremental legalization;  0.036138s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.160275s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (156.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056528s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1699/1932.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007114s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.82, top5 = 22.52, top10 = 17.71, top15 = 13.93.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055511s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.976510s wall, 1.125000s user + 0.015625s system = 1.140625s CPU (116.8%)

RUN-1003 : finish command "place" in  5.818499s wall, 8.171875s user + 3.218750s system = 11.390625s CPU (195.8%)

RUN-1004 : used memory is 196 MB, reserved memory is 160 MB, peak memory is 219 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 783 instances
RUN-1001 : 367 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1932 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1391 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6243, tnet num: 1930, tinst num: 781, tnode num: 8405, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 367 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71584, over cnt = 126(0%), over = 174, worst = 5
PHY-1002 : len = 72232, over cnt = 60(0%), over = 75, worst = 3
PHY-1002 : len = 72880, over cnt = 21(0%), over = 26, worst = 3
PHY-1002 : len = 73200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.158675s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (128.0%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.40, top10 = 17.59, top15 = 13.84.
PHY-1001 : End global routing;  0.217721s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (122.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 201, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 494, reserve = 463, peak = 494.
PHY-1001 : End build detailed router design. 3.840227s wall, 3.781250s user + 0.031250s system = 3.812500s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33008, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.441282s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 528, reserve = 496, peak = 528.
PHY-1001 : End phase 1; 1.448211s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186304, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 529.
PHY-1001 : End initial routed; 1.588217s wall, 2.781250s user + 0.203125s system = 2.984375s CPU (187.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1706(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.396   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.430   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.413880s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End phase 2; 2.002213s wall, 3.203125s user + 0.203125s system = 3.406250s CPU (170.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186304, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016963s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186224, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025871s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (120.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186288, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022683s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1706(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.396   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.430   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.415531s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.193953s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 0.822466s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 186288
PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End export database. 0.010731s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (145.6%)

PHY-1001 : End detail routing;  8.360374s wall, 9.515625s user + 0.234375s system = 9.750000s CPU (116.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6243, tnet num: 1930, tinst num: 781, tnode num: 8405, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_22.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6283, tnet num: 1950, tinst num: 801, tnode num: 8445, tedge num: 11042.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.516657s wall, 3.484375s user + 0.125000s system = 3.609375s CPU (102.6%)

RUN-1003 : finish command "route" in  12.485037s wall, 13.625000s user + 0.390625s system = 14.015625s CPU (112.3%)

RUN-1004 : used memory is 521 MB, reserved memory is 488 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      839   out of  19600    4.28%
#reg                      989   out of  19600    5.05%
#le                      1449
  #lut only               460   out of   1449   31.75%
  #reg only               610   out of   1449   42.10%
  #lut&reg                379   out of   1449   26.16%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         436
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1449   |627     |212     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1049   |320     |119     |837     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |472    |138     |44      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |61     |42      |6       |50      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |10      |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |16      |0       |18      |0       |0       |
|    integ                   |Integration                                      |135    |31      |14      |109     |0       |0       |
|    modu                    |Modulation                                       |92     |27      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |304    |89      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |113    |106     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |19      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |55      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |204    |159     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1375  
    #2          2       294   
    #3          3       119   
    #4          4        11   
    #5        5-10       80   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6283, tnet num: 1950, tinst num: 801, tnode num: 8445, tedge num: 11042.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1950 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 801
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1952, pip num: 14426
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38386 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.582744s wall, 19.953125s user + 0.046875s system = 20.000000s CPU (558.2%)

RUN-1004 : used memory is 519 MB, reserved memory is 488 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231023_145418.log"
