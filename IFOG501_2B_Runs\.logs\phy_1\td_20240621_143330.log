============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 14:33:30 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.4000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.4000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2082/10 useful/useless nets, 1283/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1855/18 useful/useless nets, 1613/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1660/15 useful/useless nets, 1418/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1684/156 useful/useless nets, 1464/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2052/5 useful/useless nets, 1832/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7585, tnet num: 2052, tinst num: 1831, tnode num: 9508, tedge num: 11632.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2052 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 176 (3.55), #lev = 8 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.57), #lev = 7 (2.02)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1337 instances
RUN-0007 : 542 luts, 585 seqs, 108 mslices, 66 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1564 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 914 nets have 2 pins
RUN-1001 : 468 nets have [3 - 5] pins
RUN-1001 : 98 nets have [6 - 10] pins
RUN-1001 : 50 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     158     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     93      
RUN-1001 :   Yes  |  No   |  No   |     57      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1335 instances, 542 luts, 585 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6532, tnet num: 1562, tinst num: 1335, tnode num: 8458, tedge num: 10744.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.122378s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 347683
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1335.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 282872, overlap = 51.75
PHY-3002 : Step(2): len = 241654, overlap = 51.75
PHY-3002 : Step(3): len = 215469, overlap = 51.75
PHY-3002 : Step(4): len = 188099, overlap = 51.75
PHY-3002 : Step(5): len = 159585, overlap = 51.75
PHY-3002 : Step(6): len = 137546, overlap = 51.75
PHY-3002 : Step(7): len = 121703, overlap = 51.75
PHY-3002 : Step(8): len = 104814, overlap = 51.75
PHY-3002 : Step(9): len = 90495.1, overlap = 53.0625
PHY-3002 : Step(10): len = 82365.7, overlap = 53.5312
PHY-3002 : Step(11): len = 74265.9, overlap = 53.875
PHY-3002 : Step(12): len = 67156, overlap = 48.9375
PHY-3002 : Step(13): len = 61217.4, overlap = 48.6875
PHY-3002 : Step(14): len = 56849.7, overlap = 49.0938
PHY-3002 : Step(15): len = 53108.7, overlap = 49.3438
PHY-3002 : Step(16): len = 48696.7, overlap = 51
PHY-3002 : Step(17): len = 46940.7, overlap = 53.5312
PHY-3002 : Step(18): len = 45471.9, overlap = 53.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.20081e-05
PHY-3002 : Step(19): len = 46898.2, overlap = 46.5625
PHY-3002 : Step(20): len = 48676.6, overlap = 44.125
PHY-3002 : Step(21): len = 47386.4, overlap = 51.3438
PHY-3002 : Step(22): len = 45882.2, overlap = 47.0625
PHY-3002 : Step(23): len = 44881, overlap = 40.4688
PHY-3002 : Step(24): len = 44252.9, overlap = 49.2812
PHY-3002 : Step(25): len = 43769.3, overlap = 44.7812
PHY-3002 : Step(26): len = 43503.6, overlap = 40.375
PHY-3002 : Step(27): len = 42442.1, overlap = 35.875
PHY-3002 : Step(28): len = 41639.2, overlap = 35.125
PHY-3002 : Step(29): len = 40530.4, overlap = 34.5625
PHY-3002 : Step(30): len = 39949.5, overlap = 34.0625
PHY-3002 : Step(31): len = 39531.7, overlap = 34
PHY-3002 : Step(32): len = 39341.9, overlap = 33.75
PHY-3002 : Step(33): len = 38645.9, overlap = 31.5
PHY-3002 : Step(34): len = 37824.1, overlap = 36
PHY-3002 : Step(35): len = 36800.9, overlap = 38.25
PHY-3002 : Step(36): len = 35687.4, overlap = 36
PHY-3002 : Step(37): len = 35101.7, overlap = 36
PHY-3002 : Step(38): len = 34700.9, overlap = 36
PHY-3002 : Step(39): len = 34439.5, overlap = 36
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.40162e-05
PHY-3002 : Step(40): len = 34644.3, overlap = 36
PHY-3002 : Step(41): len = 34781.7, overlap = 36
PHY-3002 : Step(42): len = 34904, overlap = 36
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.80324e-05
PHY-3002 : Step(43): len = 35055.8, overlap = 36
PHY-3002 : Step(44): len = 35099.2, overlap = 36
PHY-3002 : Step(45): len = 35410.7, overlap = 38.25
PHY-3002 : Step(46): len = 35417, overlap = 38.25
PHY-3002 : Step(47): len = 35225.5, overlap = 33.75
PHY-3002 : Step(48): len = 35049, overlap = 31.5
PHY-3002 : Step(49): len = 34838.9, overlap = 29.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005968s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (261.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036541s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(50): len = 36798.5, overlap = 3.25
PHY-3002 : Step(51): len = 36907.6, overlap = 3.4375
PHY-3002 : Step(52): len = 37013.8, overlap = 3.625
PHY-3002 : Step(53): len = 36936.3, overlap = 4.40625
PHY-3002 : Step(54): len = 36991.8, overlap = 5.15625
PHY-3002 : Step(55): len = 37039.3, overlap = 6
PHY-3002 : Step(56): len = 36706.5, overlap = 7.59375
PHY-3002 : Step(57): len = 36510.4, overlap = 8.90625
PHY-3002 : Step(58): len = 36253.7, overlap = 9.34375
PHY-3002 : Step(59): len = 35759.1, overlap = 9.59375
PHY-3002 : Step(60): len = 35286.1, overlap = 11.1562
PHY-3002 : Step(61): len = 34440.6, overlap = 13.2812
PHY-3002 : Step(62): len = 33826, overlap = 17.9688
PHY-3002 : Step(63): len = 33433.1, overlap = 18.25
PHY-3002 : Step(64): len = 33124.7, overlap = 18.875
PHY-3002 : Step(65): len = 32555.6, overlap = 19.6562
PHY-3002 : Step(66): len = 32098.3, overlap = 22.6562
PHY-3002 : Step(67): len = 31706.5, overlap = 22.75
PHY-3002 : Step(68): len = 31588.8, overlap = 22.9375
PHY-3002 : Step(69): len = 31364.1, overlap = 24.125
PHY-3002 : Step(70): len = 31086.3, overlap = 26.0312
PHY-3002 : Step(71): len = 30961.3, overlap = 28.5625
PHY-3002 : Step(72): len = 30827.8, overlap = 30.0938
PHY-3002 : Step(73): len = 30742.5, overlap = 30.9688
PHY-3002 : Step(74): len = 30486.4, overlap = 31.6562
PHY-3002 : Step(75): len = 30574.3, overlap = 31.2812
PHY-3002 : Step(76): len = 30331.9, overlap = 32.0625
PHY-3002 : Step(77): len = 30299.1, overlap = 32.2188
PHY-3002 : Step(78): len = 30180.4, overlap = 31.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000269455
PHY-3002 : Step(79): len = 30107.2, overlap = 32.0312
PHY-3002 : Step(80): len = 30216.3, overlap = 29.8438
PHY-3002 : Step(81): len = 30527.3, overlap = 29.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000538909
PHY-3002 : Step(82): len = 30769.3, overlap = 29.9062
PHY-3002 : Step(83): len = 31011.6, overlap = 29.2812
PHY-3002 : Step(84): len = 31679, overlap = 28.4688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036194s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.63019e-05
PHY-3002 : Step(85): len = 31688.7, overlap = 57
PHY-3002 : Step(86): len = 32052.5, overlap = 55.625
PHY-3002 : Step(87): len = 33466.3, overlap = 46.75
PHY-3002 : Step(88): len = 33930.6, overlap = 39.1875
PHY-3002 : Step(89): len = 33636.4, overlap = 42.2188
PHY-3002 : Step(90): len = 33595.7, overlap = 40.5625
PHY-3002 : Step(91): len = 33115.5, overlap = 39.5312
PHY-3002 : Step(92): len = 32849.3, overlap = 39.625
PHY-3002 : Step(93): len = 32394.6, overlap = 37.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.26039e-05
PHY-3002 : Step(94): len = 32317.4, overlap = 36.9688
PHY-3002 : Step(95): len = 32317.4, overlap = 36.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000185208
PHY-3002 : Step(96): len = 32854.9, overlap = 39.0312
PHY-3002 : Step(97): len = 32854.9, overlap = 39.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000370416
PHY-3002 : Step(98): len = 33110.3, overlap = 38.75
PHY-3002 : Step(99): len = 33344.4, overlap = 37.2188
PHY-3002 : Step(100): len = 34003, overlap = 35.125
PHY-3002 : Step(101): len = 33962.7, overlap = 33.875
PHY-3002 : Step(102): len = 33701.9, overlap = 31.7812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000740831
PHY-3002 : Step(103): len = 33609.6, overlap = 31.9688
PHY-3002 : Step(104): len = 33629.4, overlap = 31.8438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00148166
PHY-3002 : Step(105): len = 33716.7, overlap = 32.0938
PHY-3002 : Step(106): len = 33716.7, overlap = 32.0938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00296332
PHY-3002 : Step(107): len = 34114.1, overlap = 30.7188
PHY-3002 : Step(108): len = 34114.1, overlap = 30.7188
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00592665
PHY-3002 : Step(109): len = 34213.9, overlap = 29.8125
PHY-3002 : Step(110): len = 34308.3, overlap = 29.25
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0118533
PHY-3002 : Step(111): len = 34310.9, overlap = 28.8438
PHY-3002 : Step(112): len = 34310.9, overlap = 28.8438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6532, tnet num: 1562, tinst num: 1335, tnode num: 8458, tedge num: 10744.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 77.34 peak overflow 3.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1564.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 41664, over cnt = 183(0%), over = 710, worst = 24
PHY-1001 : End global iterations;  0.063530s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.4%)

PHY-1001 : Congestion index: top1 = 38.49, top5 = 20.65, top10 = 12.73, top15 = 9.05.
PHY-1001 : End incremental global routing;  0.110459s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1562 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045677s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1323 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1357 instances, 542 luts, 607 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 34648
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6620, tnet num: 1584, tinst num: 1357, tnode num: 8612, tedge num: 10876.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1584 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.135396s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(113): len = 34622.4, overlap = 0
PHY-3002 : Step(114): len = 34720.3, overlap = 0
PHY-3002 : Step(115): len = 34836.2, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1584 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037021s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(116): len = 34717.5, overlap = 29.0312
PHY-3002 : Step(117): len = 34717.5, overlap = 29.0312
PHY-3001 : Final: Len = 34717.5, Over = 29.0312
PHY-3001 : End incremental placement;  0.246864s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (107.6%)

OPT-1001 : Total overflow 78.22 peak overflow 3.81
OPT-1001 : End high-fanout net optimization;  0.431931s wall, 0.406250s user + 0.031250s system = 0.437500s CPU (101.3%)

OPT-1001 : Current memory(MB): used = 196, reserve = 150, peak = 196.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1075/1586.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 42336, over cnt = 181(0%), over = 702, worst = 24
PHY-1002 : len = 47416, over cnt = 132(0%), over = 287, worst = 13
PHY-1002 : len = 49792, over cnt = 60(0%), over = 102, worst = 11
PHY-1002 : len = 50664, over cnt = 20(0%), over = 31, worst = 4
PHY-1002 : len = 51256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096042s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.6%)

PHY-1001 : Congestion index: top1 = 35.11, top5 = 21.74, top10 = 14.70, top15 = 10.61.
OPT-1001 : End congestion update;  0.138737s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1584 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038003s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (82.2%)

OPT-0007 : Start: WNS 4948 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.176945s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.1%)

OPT-1001 : Current memory(MB): used = 194, reserve = 148, peak = 196.
OPT-1001 : End physical optimization;  0.732330s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (102.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 542 LUT to BLE ...
SYN-4008 : Packed 542 LUT and 196 SEQ to BLE.
SYN-4003 : Packing 411 remaining SEQ's ...
SYN-4005 : Packed 233 SEQ with LUT/SLICE
SYN-4006 : 140 single LUT's are left
SYN-4006 : 178 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 720/1064 primitive instances ...
PHY-3001 : End packing;  0.040034s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (117.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 613 instances
RUN-1001 : 288 mslices, 289 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1392 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 705 nets have 2 pins
RUN-1001 : 497 nets have [3 - 5] pins
RUN-1001 : 115 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 25 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 611 instances, 577 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 35285.2, Over = 44.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5806, tnet num: 1390, tinst num: 611, tnode num: 7290, tedge num: 9885.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.147911s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.8167e-05
PHY-3002 : Step(118): len = 34761.1, overlap = 46.25
PHY-3002 : Step(119): len = 34421.8, overlap = 48.5
PHY-3002 : Step(120): len = 34019.5, overlap = 49
PHY-3002 : Step(121): len = 34090.2, overlap = 48.5
PHY-3002 : Step(122): len = 34034.4, overlap = 47.5
PHY-3002 : Step(123): len = 34331, overlap = 45.5
PHY-3002 : Step(124): len = 34567.2, overlap = 44
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000136334
PHY-3002 : Step(125): len = 34431.3, overlap = 44.25
PHY-3002 : Step(126): len = 34504.5, overlap = 44
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000272668
PHY-3002 : Step(127): len = 35018.8, overlap = 42.25
PHY-3002 : Step(128): len = 35646.7, overlap = 40.75
PHY-3002 : Step(129): len = 35775, overlap = 38.5
PHY-3002 : Step(130): len = 35974.1, overlap = 37
PHY-3002 : Step(131): len = 36137, overlap = 36.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.076025s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (185.0%)

PHY-3001 : Trial Legalized: Len = 50138.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.032511s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00332877
PHY-3002 : Step(132): len = 47289.5, overlap = 4
PHY-3002 : Step(133): len = 44893.5, overlap = 7.5
PHY-3002 : Step(134): len = 42975.2, overlap = 12.25
PHY-3002 : Step(135): len = 41635, overlap = 16
PHY-3002 : Step(136): len = 41023.4, overlap = 16.25
PHY-3002 : Step(137): len = 40187.9, overlap = 16.25
PHY-3002 : Step(138): len = 39589.6, overlap = 18.75
PHY-3002 : Step(139): len = 39022.7, overlap = 20
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00665755
PHY-3002 : Step(140): len = 39096.3, overlap = 19.75
PHY-3002 : Step(141): len = 39087.3, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0133151
PHY-3002 : Step(142): len = 39038.3, overlap = 19
PHY-3002 : Step(143): len = 39038.3, overlap = 19
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004802s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (325.4%)

PHY-3001 : Legalized: Len = 44916.5, Over = 0
PHY-3001 : End spreading;  0.003884s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 44916.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5806, tnet num: 1390, tinst num: 611, tnode num: 7290, tedge num: 9885.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 45/1392.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53912, over cnt = 162(0%), over = 271, worst = 6
PHY-1002 : len = 55424, over cnt = 71(0%), over = 100, worst = 4
PHY-1002 : len = 56360, over cnt = 17(0%), over = 24, worst = 4
PHY-1002 : len = 56608, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 56656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132840s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (176.4%)

PHY-1001 : Congestion index: top1 = 28.02, top5 = 21.39, top10 = 15.84, top15 = 11.78.
PHY-1001 : End incremental global routing;  0.180702s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (164.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (115.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.247348s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (145.3%)

OPT-1001 : Current memory(MB): used = 199, reserve = 154, peak = 199.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1192/1392.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005396s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.02, top5 = 21.39, top10 = 15.84, top15 = 11.78.
OPT-1001 : End congestion update;  0.047493s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033621s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (139.4%)

OPT-0007 : Start: WNS 5107 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.081317s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 201, reserve = 155, peak = 201.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032226s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1192/1392.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004653s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (335.8%)

PHY-1001 : Congestion index: top1 = 28.02, top5 = 21.39, top10 = 15.84, top15 = 11.78.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032805s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 5107 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 27.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 5107ps with logic level 7 
OPT-1001 : End physical optimization;  0.585249s wall, 0.671875s user + 0.031250s system = 0.703125s CPU (120.1%)

RUN-1003 : finish command "place" in  4.108634s wall, 6.062500s user + 2.421875s system = 8.484375s CPU (206.5%)

RUN-1004 : used memory is 192 MB, reserved memory is 145 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 613 instances
RUN-1001 : 288 mslices, 289 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1392 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 705 nets have 2 pins
RUN-1001 : 497 nets have [3 - 5] pins
RUN-1001 : 115 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 25 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5806, tnet num: 1390, tinst num: 611, tnode num: 7290, tedge num: 9885.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 288 mslices, 289 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53504, over cnt = 164(0%), over = 272, worst = 6
PHY-1002 : len = 54624, over cnt = 93(0%), over = 141, worst = 5
PHY-1002 : len = 56344, over cnt = 13(0%), over = 21, worst = 4
PHY-1002 : len = 56600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126259s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (148.5%)

PHY-1001 : Congestion index: top1 = 28.79, top5 = 21.52, top10 = 15.87, top15 = 11.84.
PHY-1001 : End global routing;  0.172315s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (136.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 219, reserve = 174, peak = 219.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 480, reserve = 439, peak = 480.
PHY-1001 : End build detailed router design. 3.182027s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 25408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.593954s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 511, reserve = 473, peak = 511.
PHY-1001 : End phase 1; 0.599580s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179368, over cnt = 60(0%), over = 60, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 511, reserve = 473, peak = 511.
PHY-1001 : End initial routed; 1.626588s wall, 2.062500s user + 0.109375s system = 2.171875s CPU (133.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   3.529   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.196301s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.5%)

PHY-1001 : Current memory(MB): used = 512, reserve = 473, peak = 512.
PHY-1001 : End phase 2; 1.822975s wall, 2.265625s user + 0.109375s system = 2.375000s CPU (130.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179368, over cnt = 60(0%), over = 60, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013937s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (112.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179064, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.057779s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026572s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (58.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   3.511   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.224084s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.164974s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.7%)

PHY-1001 : Current memory(MB): used = 525, reserve = 484, peak = 525.
PHY-1001 : End phase 3; 0.600714s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (101.4%)

PHY-1003 : Routed, final wirelength = 179160
PHY-1001 : Current memory(MB): used = 526, reserve = 484, peak = 526.
PHY-1001 : End export database. 0.009023s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (173.2%)

PHY-1001 : End detail routing;  6.382142s wall, 6.781250s user + 0.156250s system = 6.937500s CPU (108.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5806, tnet num: 1390, tinst num: 611, tnode num: 7290, tedge num: 9885.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  6.938264s wall, 7.390625s user + 0.171875s system = 7.562500s CPU (109.0%)

RUN-1004 : used memory is 504 MB, reserved memory is 463 MB, peak memory is 526 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      911   out of  19600    4.65%
#reg                      610   out of  19600    3.11%
#le                      1089
  #lut only               479   out of   1089   43.99%
  #reg only               178   out of   1089   16.35%
  #lut&reg                432   out of   1089   39.67%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    292
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         104
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1089   |737     |174     |616     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |114    |92      |11      |61      |0       |0       |
|    usms                            |Time_1ms        |28     |14      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |182    |123     |23      |131     |0       |0       |
|  uart                              |UART_Control    |111    |102     |4       |57      |0       |0       |
|    U0                              |speed_select_Tx |21     |12      |4       |15      |0       |0       |
|    U1                              |uart_tx         |23     |23      |0       |15      |0       |0       |
|    U2                              |Ctrl_Data       |67     |67      |0       |27      |0       |0       |
|  wendu                             |DS18B20         |170    |129     |41      |41      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |476    |270     |87      |298     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |476    |270     |87      |298     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |185    |93      |0       |176     |0       |0       |
|        reg_inst                    |register        |182    |90      |0       |173     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |291    |177     |87      |122     |0       |0       |
|        bus_inst                    |bus_top         |77     |51      |26      |27      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |77     |51      |26      |27      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |132    |88      |29      |71      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       696   
    #2          2       303   
    #3          3       149   
    #4          4        45   
    #5        5-10      118   
    #6        11-50      60   
    #7       51-100      1    
    #8       101-500     1    
  Average     2.91            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5806, tnet num: 1390, tinst num: 611, tnode num: 7290, tedge num: 9885.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1390 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 611
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1392, pip num: 13792
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1200 valid insts, and 38140 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.050243s wall, 15.703125s user + 0.046875s system = 15.750000s CPU (516.4%)

RUN-1004 : used memory is 528 MB, reserved memory is 486 MB, peak memory is 649 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_143330.log"
