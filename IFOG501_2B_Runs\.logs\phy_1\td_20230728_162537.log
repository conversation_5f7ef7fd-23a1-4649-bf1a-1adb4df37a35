============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 28 16:25:37 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1613 instances
RUN-0007 : 373 luts, 991 seqs, 128 mslices, 72 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2137 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1604 nets have 2 pins
RUN-1001 : 417 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1611 instances, 373 luts, 991 seqs, 200 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7622, tnet num: 2135, tinst num: 1611, tnode num: 10812, tedge num: 12824.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.292629s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (101.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 567902
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1611.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 478404, overlap = 18
PHY-3002 : Step(2): len = 448592, overlap = 20.25
PHY-3002 : Step(3): len = 430069, overlap = 20.25
PHY-3002 : Step(4): len = 411758, overlap = 18
PHY-3002 : Step(5): len = 402978, overlap = 20.25
PHY-3002 : Step(6): len = 392613, overlap = 20.25
PHY-3002 : Step(7): len = 346445, overlap = 20.25
PHY-3002 : Step(8): len = 301916, overlap = 20.25
PHY-3002 : Step(9): len = 293662, overlap = 20.25
PHY-3002 : Step(10): len = 287518, overlap = 20.25
PHY-3002 : Step(11): len = 278264, overlap = 20.25
PHY-3002 : Step(12): len = 269187, overlap = 20.25
PHY-3002 : Step(13): len = 263703, overlap = 20.25
PHY-3002 : Step(14): len = 254136, overlap = 20.25
PHY-3002 : Step(15): len = 247158, overlap = 20.25
PHY-3002 : Step(16): len = 240904, overlap = 20.25
PHY-3002 : Step(17): len = 234817, overlap = 20.25
PHY-3002 : Step(18): len = 226894, overlap = 20.25
PHY-3002 : Step(19): len = 221914, overlap = 20.25
PHY-3002 : Step(20): len = 217335, overlap = 20.25
PHY-3002 : Step(21): len = 210983, overlap = 20.25
PHY-3002 : Step(22): len = 204683, overlap = 20.25
PHY-3002 : Step(23): len = 201477, overlap = 20.25
PHY-3002 : Step(24): len = 195536, overlap = 20.25
PHY-3002 : Step(25): len = 189304, overlap = 20.25
PHY-3002 : Step(26): len = 186663, overlap = 20.25
PHY-3002 : Step(27): len = 182099, overlap = 20.25
PHY-3002 : Step(28): len = 170696, overlap = 20.25
PHY-3002 : Step(29): len = 166463, overlap = 20.25
PHY-3002 : Step(30): len = 164630, overlap = 20.25
PHY-3002 : Step(31): len = 154283, overlap = 20.25
PHY-3002 : Step(32): len = 142631, overlap = 20.25
PHY-3002 : Step(33): len = 140768, overlap = 20.25
PHY-3002 : Step(34): len = 136880, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118385
PHY-3002 : Step(35): len = 138585, overlap = 15.75
PHY-3002 : Step(36): len = 137897, overlap = 15.75
PHY-3002 : Step(37): len = 135620, overlap = 13.5
PHY-3002 : Step(38): len = 132233, overlap = 15.75
PHY-3002 : Step(39): len = 130405, overlap = 11.25
PHY-3002 : Step(40): len = 129169, overlap = 11.25
PHY-3002 : Step(41): len = 126578, overlap = 9
PHY-3002 : Step(42): len = 122143, overlap = 15.75
PHY-3002 : Step(43): len = 121214, overlap = 6.75
PHY-3002 : Step(44): len = 118262, overlap = 11.25
PHY-3002 : Step(45): len = 115267, overlap = 11.25
PHY-3002 : Step(46): len = 111036, overlap = 11.25
PHY-3002 : Step(47): len = 110222, overlap = 11.25
PHY-3002 : Step(48): len = 108008, overlap = 11.25
PHY-3002 : Step(49): len = 101842, overlap = 11.25
PHY-3002 : Step(50): len = 99099.8, overlap = 9
PHY-3002 : Step(51): len = 97173.7, overlap = 9
PHY-3002 : Step(52): len = 96812.5, overlap = 11.25
PHY-3002 : Step(53): len = 94841.3, overlap = 11.25
PHY-3002 : Step(54): len = 93646.1, overlap = 11.25
PHY-3002 : Step(55): len = 90936.8, overlap = 13.5
PHY-3002 : Step(56): len = 87727.2, overlap = 13.5
PHY-3002 : Step(57): len = 85840.9, overlap = 13.5
PHY-3002 : Step(58): len = 85518, overlap = 11.25
PHY-3002 : Step(59): len = 84102, overlap = 6.75
PHY-3002 : Step(60): len = 82929.1, overlap = 9
PHY-3002 : Step(61): len = 82304, overlap = 13.5
PHY-3002 : Step(62): len = 81175.9, overlap = 13.5
PHY-3002 : Step(63): len = 80295.3, overlap = 13.5
PHY-3002 : Step(64): len = 78559.1, overlap = 11.25
PHY-3002 : Step(65): len = 78039.9, overlap = 11.25
PHY-3002 : Step(66): len = 76383.1, overlap = 11.25
PHY-3002 : Step(67): len = 75612.2, overlap = 13.625
PHY-3002 : Step(68): len = 74535.8, overlap = 13.75
PHY-3002 : Step(69): len = 73744.5, overlap = 11.4375
PHY-3002 : Step(70): len = 70058.3, overlap = 11.25
PHY-3002 : Step(71): len = 69344.9, overlap = 11.25
PHY-3002 : Step(72): len = 68534.3, overlap = 11.25
PHY-3002 : Step(73): len = 68012.2, overlap = 13.5
PHY-3002 : Step(74): len = 67853.1, overlap = 13.5
PHY-3002 : Step(75): len = 67316.6, overlap = 11.25
PHY-3002 : Step(76): len = 66774.4, overlap = 11.25
PHY-3002 : Step(77): len = 66542.5, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00023677
PHY-3002 : Step(78): len = 66650.7, overlap = 11.25
PHY-3002 : Step(79): len = 66719.5, overlap = 11.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00047354
PHY-3002 : Step(80): len = 66661.7, overlap = 6.75
PHY-3002 : Step(81): len = 66644.8, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006480s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062748s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 70032.3, overlap = 5.28125
PHY-3002 : Step(83): len = 68624.6, overlap = 4.78125
PHY-3002 : Step(84): len = 67280, overlap = 4.65625
PHY-3002 : Step(85): len = 65439.2, overlap = 4.71875
PHY-3002 : Step(86): len = 63996.9, overlap = 4.46875
PHY-3002 : Step(87): len = 62867.2, overlap = 4.53125
PHY-3002 : Step(88): len = 61645.5, overlap = 5.875
PHY-3002 : Step(89): len = 60144.2, overlap = 6.625
PHY-3002 : Step(90): len = 58464.6, overlap = 8.6875
PHY-3002 : Step(91): len = 56964.6, overlap = 8
PHY-3002 : Step(92): len = 56146, overlap = 7.5625
PHY-3002 : Step(93): len = 55039.8, overlap = 8.1875
PHY-3002 : Step(94): len = 53936.2, overlap = 8.6875
PHY-3002 : Step(95): len = 53157.1, overlap = 9.5
PHY-3002 : Step(96): len = 52377.4, overlap = 9.6875
PHY-3002 : Step(97): len = 51840.6, overlap = 10.625
PHY-3002 : Step(98): len = 51014.1, overlap = 11.8125
PHY-3002 : Step(99): len = 50158.7, overlap = 12
PHY-3002 : Step(100): len = 49318.9, overlap = 13.8125
PHY-3002 : Step(101): len = 48781.2, overlap = 14.75
PHY-3002 : Step(102): len = 48588.7, overlap = 14.3125
PHY-3002 : Step(103): len = 48238.4, overlap = 15.0625
PHY-3002 : Step(104): len = 47922, overlap = 15.7812
PHY-3002 : Step(105): len = 47838.6, overlap = 15.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000813385
PHY-3002 : Step(106): len = 47739.9, overlap = 15.9688
PHY-3002 : Step(107): len = 47548.4, overlap = 15.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00162677
PHY-3002 : Step(108): len = 47492.9, overlap = 15.6562
PHY-3002 : Step(109): len = 47278.3, overlap = 16.6562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073857s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.87897e-05
PHY-3002 : Step(110): len = 48220.4, overlap = 60.4688
PHY-3002 : Step(111): len = 49279.5, overlap = 58
PHY-3002 : Step(112): len = 49238.3, overlap = 52.9062
PHY-3002 : Step(113): len = 49542.9, overlap = 48.3125
PHY-3002 : Step(114): len = 49920.5, overlap = 47.0312
PHY-3002 : Step(115): len = 49707.8, overlap = 44
PHY-3002 : Step(116): len = 49288.2, overlap = 43.9688
PHY-3002 : Step(117): len = 49131, overlap = 43.8438
PHY-3002 : Step(118): len = 48957.5, overlap = 42.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000177579
PHY-3002 : Step(119): len = 48970.8, overlap = 43.0938
PHY-3002 : Step(120): len = 49094.4, overlap = 42.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000355159
PHY-3002 : Step(121): len = 49351.3, overlap = 43.625
PHY-3002 : Step(122): len = 50032.5, overlap = 39.4688
PHY-3002 : Step(123): len = 51199.6, overlap = 32.7812
PHY-3002 : Step(124): len = 51731.5, overlap = 31.5938
PHY-3002 : Step(125): len = 51519.1, overlap = 28.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7622, tnet num: 2135, tinst num: 1611, tnode num: 10812, tedge num: 12824.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 71.62 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2137.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53672, over cnt = 222(0%), over = 954, worst = 16
PHY-1001 : End global iterations;  0.064112s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (170.6%)

PHY-1001 : Congestion index: top1 = 41.53, top5 = 24.12, top10 = 15.51, top15 = 11.07.
PHY-1001 : End incremental global routing;  0.116915s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (133.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071552s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (109.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.220712s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (120.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1604/2137.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53672, over cnt = 222(0%), over = 954, worst = 16
PHY-1002 : len = 59200, over cnt = 146(0%), over = 390, worst = 14
PHY-1002 : len = 63608, over cnt = 35(0%), over = 64, worst = 12
PHY-1002 : len = 64464, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 64512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096904s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 36.06, top5 = 24.56, top10 = 17.16, top15 = 12.69.
OPT-1001 : End congestion update;  0.142255s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (120.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061100s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206161s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.706827s wall, 0.750000s user + 0.046875s system = 0.796875s CPU (112.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 810 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 697 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1377 primitive instances ...
PHY-3001 : End packing;  0.052438s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 813 instances
RUN-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1970 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1445 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 811 instances, 764 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51177.4, Over = 62.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6399, tnet num: 1968, tinst num: 811, tnode num: 8694, tedge num: 11195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.324617s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (96.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.20938e-05
PHY-3002 : Step(126): len = 50753.2, overlap = 63.25
PHY-3002 : Step(127): len = 50470, overlap = 62.75
PHY-3002 : Step(128): len = 50504.9, overlap = 63.5
PHY-3002 : Step(129): len = 50095.4, overlap = 63.75
PHY-3002 : Step(130): len = 49993.3, overlap = 63.75
PHY-3002 : Step(131): len = 49855.4, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.41876e-05
PHY-3002 : Step(132): len = 50312.2, overlap = 62
PHY-3002 : Step(133): len = 50715.9, overlap = 62.25
PHY-3002 : Step(134): len = 51032.5, overlap = 60
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000128375
PHY-3002 : Step(135): len = 51733.5, overlap = 55.75
PHY-3002 : Step(136): len = 52194.2, overlap = 55.25
PHY-3002 : Step(137): len = 52441.5, overlap = 53.5
PHY-3002 : Step(138): len = 53002.2, overlap = 49
PHY-3002 : Step(139): len = 53275.3, overlap = 49.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.069569s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (314.4%)

PHY-3001 : Trial Legalized: Len = 68193.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054321s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000843637
PHY-3002 : Step(140): len = 64951.7, overlap = 7
PHY-3002 : Step(141): len = 62151, overlap = 11.25
PHY-3002 : Step(142): len = 60217.1, overlap = 15.5
PHY-3002 : Step(143): len = 59067.7, overlap = 19.75
PHY-3002 : Step(144): len = 58157.6, overlap = 22.25
PHY-3002 : Step(145): len = 57788.4, overlap = 25.25
PHY-3002 : Step(146): len = 57501.6, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00168727
PHY-3002 : Step(147): len = 57635, overlap = 26.25
PHY-3002 : Step(148): len = 57683.5, overlap = 25.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00337455
PHY-3002 : Step(149): len = 57729.6, overlap = 25.75
PHY-3002 : Step(150): len = 57740.5, overlap = 25.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005412s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (288.7%)

PHY-3001 : Legalized: Len = 62881.7, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006058s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 0, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 62931.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6399, tnet num: 1968, tinst num: 811, tnode num: 8694, tedge num: 11195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 86/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68632, over cnt = 152(0%), over = 233, worst = 8
PHY-1002 : len = 69560, over cnt = 102(0%), over = 133, worst = 5
PHY-1002 : len = 70832, over cnt = 21(0%), over = 24, worst = 2
PHY-1002 : len = 71192, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124531s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.39, top10 = 17.26, top15 = 13.74.
PHY-1001 : End incremental global routing;  0.176892s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (97.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062032s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.269956s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (98.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1762/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007096s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.39, top10 = 17.26, top15 = 13.74.
OPT-1001 : End congestion update;  0.058332s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054070s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 773 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 811 instances, 764 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63004.4, Over = 0
PHY-3001 : End spreading;  0.006193s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (252.3%)

PHY-3001 : Final: Len = 63004.4, Over = 0
PHY-3001 : End incremental legalization;  0.038809s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (120.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.165413s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051629s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1758/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009287s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.42, top10 = 17.27, top15 = 13.76.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066935s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.919670s wall, 0.906250s user + 0.062500s system = 0.968750s CPU (105.3%)

RUN-1003 : finish command "place" in  5.254248s wall, 8.734375s user + 2.203125s system = 10.937500s CPU (208.2%)

RUN-1004 : used memory is 205 MB, reserved memory is 170 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 813 instances
RUN-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1970 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1445 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6399, tnet num: 1968, tinst num: 811, tnode num: 8694, tedge num: 11195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67992, over cnt = 153(0%), over = 232, worst = 8
PHY-1002 : len = 69104, over cnt = 87(0%), over = 113, worst = 4
PHY-1002 : len = 70232, over cnt = 22(0%), over = 27, worst = 2
PHY-1002 : len = 70584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124425s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (138.1%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.25, top10 = 17.12, top15 = 13.57.
PHY-1001 : End global routing;  0.176191s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (124.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 468, peak = 498.
PHY-1001 : End build detailed router design. 3.357699s wall, 3.234375s user + 0.078125s system = 3.312500s CPU (98.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33568, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.412111s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.418522s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 179200, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.164754s wall, 2.140625s user + 0.078125s system = 2.218750s CPU (190.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1757(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.729   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373202s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.538054s wall, 2.515625s user + 0.078125s system = 2.593750s CPU (168.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179200, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016763s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (93.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179112, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025292s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179096, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.023144s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (67.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179128, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019887s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (157.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1757(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.729   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.371459s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.183126s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (93.9%)

PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End phase 3; 0.768444s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 179128
PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End export database. 0.011072s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.1%)

PHY-1001 : End detail routing;  7.278689s wall, 8.109375s user + 0.187500s system = 8.296875s CPU (114.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6399, tnet num: 1968, tinst num: 811, tnode num: 8694, tedge num: 11195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.211231s wall, 9.046875s user + 0.218750s system = 9.265625s CPU (112.8%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      778   out of  19600    3.97%
#reg                     1045   out of  19600    5.33%
#le                      1475
  #lut only               430   out of   1475   29.15%
  #reg only               697   out of   1475   47.25%
  #lut&reg                348   out of   1475   23.59%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         457
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1475   |578     |200     |1076    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1094   |299     |121     |896     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |34     |27      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |524    |116     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |166    |60      |20      |132     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |54     |0       |0       |54      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |15      |0       |27      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |62     |26      |14      |58      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |93      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |20      |4       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |97     |82      |7       |53      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |21     |17      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |40     |37      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1409  
    #2          2       284   
    #3          3       104   
    #4          4        20   
    #5        5-10       81   
    #6        11-50      27   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6399, tnet num: 1968, tinst num: 811, tnode num: 8694, tedge num: 11195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 811
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1970, pip num: 14431
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1275 valid insts, and 38023 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.362456s wall, 18.234375s user + 0.109375s system = 18.343750s CPU (545.5%)

RUN-1004 : used memory is 550 MB, reserved memory is 519 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230728_162537.log"
