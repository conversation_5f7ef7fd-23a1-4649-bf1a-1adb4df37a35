============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:16:22 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2218 nets
RUN-1001 : 1659 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7846, tnet num: 2216, tinst num: 1645, tnode num: 11087, tedge num: 13245.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.285046s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 598894
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 469349, overlap = 20.25
PHY-3002 : Step(2): len = 359507, overlap = 13.5
PHY-3002 : Step(3): len = 337477, overlap = 18
PHY-3002 : Step(4): len = 325233, overlap = 20.25
PHY-3002 : Step(5): len = 318358, overlap = 18
PHY-3002 : Step(6): len = 309254, overlap = 20.25
PHY-3002 : Step(7): len = 300777, overlap = 20.25
PHY-3002 : Step(8): len = 296379, overlap = 18
PHY-3002 : Step(9): len = 288228, overlap = 18
PHY-3002 : Step(10): len = 282559, overlap = 18
PHY-3002 : Step(11): len = 276132, overlap = 18
PHY-3002 : Step(12): len = 272063, overlap = 20.25
PHY-3002 : Step(13): len = 264252, overlap = 20.25
PHY-3002 : Step(14): len = 260559, overlap = 20.25
PHY-3002 : Step(15): len = 254320, overlap = 20.25
PHY-3002 : Step(16): len = 249686, overlap = 20.25
PHY-3002 : Step(17): len = 244428, overlap = 20.25
PHY-3002 : Step(18): len = 240842, overlap = 20.25
PHY-3002 : Step(19): len = 231623, overlap = 18
PHY-3002 : Step(20): len = 226177, overlap = 18
PHY-3002 : Step(21): len = 222837, overlap = 18
PHY-3002 : Step(22): len = 216006, overlap = 18
PHY-3002 : Step(23): len = 202892, overlap = 18
PHY-3002 : Step(24): len = 200928, overlap = 18
PHY-3002 : Step(25): len = 195476, overlap = 18
PHY-3002 : Step(26): len = 168604, overlap = 18
PHY-3002 : Step(27): len = 159704, overlap = 18
PHY-3002 : Step(28): len = 158738, overlap = 18
PHY-3002 : Step(29): len = 147721, overlap = 18
PHY-3002 : Step(30): len = 126362, overlap = 20.25
PHY-3002 : Step(31): len = 123787, overlap = 20.25
PHY-3002 : Step(32): len = 119323, overlap = 20.25
PHY-3002 : Step(33): len = 117589, overlap = 20.25
PHY-3002 : Step(34): len = 114493, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.26535e-05
PHY-3002 : Step(35): len = 116523, overlap = 9
PHY-3002 : Step(36): len = 116400, overlap = 6.75
PHY-3002 : Step(37): len = 114325, overlap = 11.25
PHY-3002 : Step(38): len = 112821, overlap = 13.5
PHY-3002 : Step(39): len = 110744, overlap = 15.75
PHY-3002 : Step(40): len = 105399, overlap = 6.75
PHY-3002 : Step(41): len = 102063, overlap = 11.25
PHY-3002 : Step(42): len = 101068, overlap = 11.25
PHY-3002 : Step(43): len = 98395.9, overlap = 9
PHY-3002 : Step(44): len = 98335.2, overlap = 11.25
PHY-3002 : Step(45): len = 97141.3, overlap = 6.75
PHY-3002 : Step(46): len = 93727.2, overlap = 6.75
PHY-3002 : Step(47): len = 91270.7, overlap = 9
PHY-3002 : Step(48): len = 89088.5, overlap = 11.25
PHY-3002 : Step(49): len = 88299, overlap = 9
PHY-3002 : Step(50): len = 87093.9, overlap = 4.5
PHY-3002 : Step(51): len = 85056.4, overlap = 2.25
PHY-3002 : Step(52): len = 83852.6, overlap = 9
PHY-3002 : Step(53): len = 82019.9, overlap = 9
PHY-3002 : Step(54): len = 80203.3, overlap = 9
PHY-3002 : Step(55): len = 79190.5, overlap = 9
PHY-3002 : Step(56): len = 78144.7, overlap = 4.5
PHY-3002 : Step(57): len = 76591, overlap = 4.5
PHY-3002 : Step(58): len = 75351, overlap = 9
PHY-3002 : Step(59): len = 74088.7, overlap = 9
PHY-3002 : Step(60): len = 73831.3, overlap = 9
PHY-3002 : Step(61): len = 73736.3, overlap = 6.75
PHY-3002 : Step(62): len = 72967.8, overlap = 6.75
PHY-3002 : Step(63): len = 71528.6, overlap = 11.25
PHY-3002 : Step(64): len = 69807.5, overlap = 11.25
PHY-3002 : Step(65): len = 68103.1, overlap = 11.25
PHY-3002 : Step(66): len = 67017.6, overlap = 9
PHY-3002 : Step(67): len = 66200, overlap = 11.25
PHY-3002 : Step(68): len = 65324.3, overlap = 9
PHY-3002 : Step(69): len = 64550.5, overlap = 9
PHY-3002 : Step(70): len = 63952.7, overlap = 6.75
PHY-3002 : Step(71): len = 63460.2, overlap = 4.5
PHY-3002 : Step(72): len = 62539.7, overlap = 4.5
PHY-3002 : Step(73): len = 61671.4, overlap = 9
PHY-3002 : Step(74): len = 60932.6, overlap = 9
PHY-3002 : Step(75): len = 60567.9, overlap = 11.25
PHY-3002 : Step(76): len = 60348.3, overlap = 11.25
PHY-3002 : Step(77): len = 60259.7, overlap = 6.75
PHY-3002 : Step(78): len = 59472.3, overlap = 9.25
PHY-3002 : Step(79): len = 58044.1, overlap = 9.75
PHY-3002 : Step(80): len = 57772.6, overlap = 9.75
PHY-3002 : Step(81): len = 57698.5, overlap = 9.75
PHY-3002 : Step(82): len = 57212.7, overlap = 9.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000185307
PHY-3002 : Step(83): len = 57236.9, overlap = 9.75
PHY-3002 : Step(84): len = 57296.8, overlap = 7.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000370614
PHY-3002 : Step(85): len = 57381.1, overlap = 5.25
PHY-3002 : Step(86): len = 57472, overlap = 5.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007059s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062675s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(87): len = 60428.4, overlap = 8
PHY-3002 : Step(88): len = 59538.5, overlap = 7.8125
PHY-3002 : Step(89): len = 59098.8, overlap = 7.6875
PHY-3002 : Step(90): len = 57989.2, overlap = 6.9375
PHY-3002 : Step(91): len = 57235.9, overlap = 6.875
PHY-3002 : Step(92): len = 55708.7, overlap = 6.875
PHY-3002 : Step(93): len = 54857.3, overlap = 6.1875
PHY-3002 : Step(94): len = 54064.5, overlap = 6.6875
PHY-3002 : Step(95): len = 53347.6, overlap = 6.5
PHY-3002 : Step(96): len = 51612.8, overlap = 8.0625
PHY-3002 : Step(97): len = 51092.9, overlap = 9.9375
PHY-3002 : Step(98): len = 50651.5, overlap = 9.6875
PHY-3002 : Step(99): len = 50206.3, overlap = 9.875
PHY-3002 : Step(100): len = 49483.4, overlap = 8.5
PHY-3002 : Step(101): len = 49267.6, overlap = 9.75
PHY-3002 : Step(102): len = 49067.1, overlap = 8.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00190658
PHY-3002 : Step(103): len = 48930, overlap = 8
PHY-3002 : Step(104): len = 48622.1, overlap = 8.3125
PHY-3002 : Step(105): len = 48509.3, overlap = 8.125
PHY-3002 : Step(106): len = 48494.8, overlap = 8.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00381317
PHY-3002 : Step(107): len = 48408.1, overlap = 8.125
PHY-3002 : Step(108): len = 47895.5, overlap = 8.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063115s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000118518
PHY-3002 : Step(109): len = 48407.4, overlap = 58.375
PHY-3002 : Step(110): len = 48817.4, overlap = 57.7188
PHY-3002 : Step(111): len = 48709.7, overlap = 57.9062
PHY-3002 : Step(112): len = 48757.2, overlap = 55.875
PHY-3002 : Step(113): len = 48686.8, overlap = 55.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000237036
PHY-3002 : Step(114): len = 48784.7, overlap = 55.8438
PHY-3002 : Step(115): len = 48881.8, overlap = 55.25
PHY-3002 : Step(116): len = 49228.4, overlap = 54.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000474071
PHY-3002 : Step(117): len = 49563, overlap = 47.125
PHY-3002 : Step(118): len = 50282, overlap = 43.9688
PHY-3002 : Step(119): len = 51986.5, overlap = 39.4688
PHY-3002 : Step(120): len = 52246.4, overlap = 37.9062
PHY-3002 : Step(121): len = 51994.7, overlap = 37.375
PHY-3002 : Step(122): len = 51436.8, overlap = 33.3125
PHY-3002 : Step(123): len = 51016.3, overlap = 34
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7846, tnet num: 2216, tinst num: 1645, tnode num: 11087, tedge num: 13245.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.56 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2218.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53488, over cnt = 239(0%), over = 988, worst = 16
PHY-1001 : End global iterations;  0.073521s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.3%)

PHY-1001 : Congestion index: top1 = 40.91, top5 = 23.94, top10 = 15.37, top15 = 10.94.
PHY-1001 : End incremental global routing;  0.122943s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066219s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1606 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1647 instances, 380 luts, 994 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51247.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7854, tnet num: 2218, tinst num: 1647, tnode num: 11101, tedge num: 13257.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.305116s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(124): len = 51247.6, overlap = 0.875
PHY-3002 : Step(125): len = 51247.6, overlap = 0.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061514s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00374508
PHY-3002 : Step(126): len = 51247.6, overlap = 34
PHY-3002 : Step(127): len = 51247.6, overlap = 34
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00749016
PHY-3002 : Step(128): len = 51289.4, overlap = 34
PHY-3002 : Step(129): len = 51316.3, overlap = 34
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0149803
PHY-3002 : Step(130): len = 51349.3, overlap = 34
PHY-3002 : Step(131): len = 51332.2, overlap = 34
PHY-3001 : Final: Len = 51332.2, Over = 34
PHY-3001 : End incremental placement;  0.461886s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (108.3%)

OPT-1001 : Total overflow 89.56 peak overflow 3.31
OPT-1001 : End high-fanout net optimization;  0.686762s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (104.7%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1622/2220.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53848, over cnt = 238(0%), over = 981, worst = 16
PHY-1002 : len = 58648, over cnt = 180(0%), over = 470, worst = 16
PHY-1002 : len = 63808, over cnt = 48(0%), over = 67, worst = 6
PHY-1002 : len = 64560, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 64832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.085349s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.8%)

PHY-1001 : Congestion index: top1 = 36.27, top5 = 24.26, top10 = 17.00, top15 = 12.60.
OPT-1001 : End congestion update;  0.129117s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2218 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059552s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.191327s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 222.
OPT-1001 : End physical optimization;  1.158326s wall, 1.171875s user + 0.031250s system = 1.203125s CPU (103.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 99 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 706 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1086/1419 primitive instances ...
PHY-3001 : End packing;  0.047547s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 852 instances
RUN-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 850 instances, 803 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51647.8, Over = 62
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6618, tnet num: 2044, tinst num: 850, tnode num: 8960, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310166s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.20224e-05
PHY-3002 : Step(132): len = 51144.3, overlap = 61.5
PHY-3002 : Step(133): len = 50985.6, overlap = 61.75
PHY-3002 : Step(134): len = 50653.2, overlap = 65
PHY-3002 : Step(135): len = 50596.1, overlap = 65.75
PHY-3002 : Step(136): len = 50341.3, overlap = 65.25
PHY-3002 : Step(137): len = 50288.1, overlap = 65.75
PHY-3002 : Step(138): len = 50222.6, overlap = 65.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.40448e-05
PHY-3002 : Step(139): len = 50388.4, overlap = 65.75
PHY-3002 : Step(140): len = 50575.3, overlap = 65.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000109847
PHY-3002 : Step(141): len = 51046.4, overlap = 63.25
PHY-3002 : Step(142): len = 52016.9, overlap = 64
PHY-3002 : Step(143): len = 53155, overlap = 61
PHY-3002 : Step(144): len = 53069.6, overlap = 59.5
PHY-3002 : Step(145): len = 53058, overlap = 58.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.096430s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (145.8%)

PHY-3001 : Trial Legalized: Len = 66250.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052503s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000558129
PHY-3002 : Step(146): len = 63599, overlap = 4.75
PHY-3002 : Step(147): len = 61633.1, overlap = 12.25
PHY-3002 : Step(148): len = 59932.8, overlap = 18.5
PHY-3002 : Step(149): len = 58835.5, overlap = 21.75
PHY-3002 : Step(150): len = 58016.8, overlap = 22.5
PHY-3002 : Step(151): len = 57590.3, overlap = 25.75
PHY-3002 : Step(152): len = 57352.5, overlap = 27
PHY-3002 : Step(153): len = 57032.1, overlap = 29.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00111626
PHY-3002 : Step(154): len = 57343.7, overlap = 29
PHY-3002 : Step(155): len = 57492, overlap = 28.75
PHY-3002 : Step(156): len = 57531.3, overlap = 28.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00223252
PHY-3002 : Step(157): len = 57646.6, overlap = 28
PHY-3002 : Step(158): len = 57779.1, overlap = 28
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005488s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62614.9, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006678s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 0, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 62942.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6618, tnet num: 2044, tinst num: 850, tnode num: 8960, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 110/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70088, over cnt = 172(0%), over = 253, worst = 6
PHY-1002 : len = 71056, over cnt = 90(0%), over = 118, worst = 4
PHY-1002 : len = 72296, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 72456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.120384s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 22.75, top10 = 17.91, top15 = 14.21.
PHY-1001 : End incremental global routing;  0.171223s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (118.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058806s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260303s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1813/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005535s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 22.75, top10 = 17.91, top15 = 14.21.
OPT-1001 : End congestion update;  0.052506s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051913s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 812 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 850 instances, 803 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62975.8, Over = 0
PHY-3001 : End spreading;  0.005008s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62975.8, Over = 0
PHY-3001 : End incremental legalization;  0.033351s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (140.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150209s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (114.4%)

OPT-1001 : Current memory(MB): used = 227, reserve = 191, peak = 227.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052708s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1810/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72528, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017383s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (89.9%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 22.76, top10 = 17.92, top15 = 14.22.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051596s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.877643s wall, 0.875000s user + 0.046875s system = 0.921875s CPU (105.0%)

RUN-1003 : finish command "place" in  5.672465s wall, 8.093750s user + 2.562500s system = 10.656250s CPU (187.9%)

RUN-1004 : used memory is 204 MB, reserved memory is 167 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 852 instances
RUN-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6618, tnet num: 2044, tinst num: 850, tnode num: 8960, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68984, over cnt = 164(0%), over = 249, worst = 7
PHY-1002 : len = 70128, over cnt = 88(0%), over = 105, worst = 4
PHY-1002 : len = 71208, over cnt = 17(0%), over = 20, worst = 2
PHY-1002 : len = 71488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135789s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (138.1%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.53, top10 = 17.73, top15 = 14.05.
PHY-1001 : End global routing;  0.185648s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (126.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 502, reserve = 470, peak = 502.
PHY-1001 : End build detailed router design. 3.238861s wall, 3.203125s user + 0.031250s system = 3.234375s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34080, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.250806s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 535, reserve = 505, peak = 536.
PHY-1001 : End phase 1; 1.256575s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181040, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 506, peak = 536.
PHY-1001 : End initial routed; 1.088719s wall, 2.093750s user + 0.078125s system = 2.171875s CPU (199.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1810(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.351   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365887s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 539, reserve = 508, peak = 539.
PHY-1001 : End phase 2; 1.454692s wall, 2.453125s user + 0.078125s system = 2.531250s CPU (174.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181040, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014684s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180960, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025227s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (61.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181064, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022755s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1810(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.351   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363248s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.180038s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.5%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.728282s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (100.8%)

PHY-1003 : Routed, final wirelength = 181064
PHY-1001 : Current memory(MB): used = 554, reserve = 523, peak = 554.
PHY-1001 : End export database. 0.009789s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (159.6%)

PHY-1001 : End detail routing;  6.875924s wall, 7.812500s user + 0.140625s system = 7.953125s CPU (115.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6618, tnet num: 2044, tinst num: 850, tnode num: 8960, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.791486s wall, 8.765625s user + 0.140625s system = 8.906250s CPU (114.3%)

RUN-1004 : used memory is 506 MB, reserved memory is 477 MB, peak memory is 554 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      840   out of  19600    4.29%
#reg                     1077   out of  19600    5.49%
#le                      1546
  #lut only               469   out of   1546   30.34%
  #reg only               706   out of   1546   45.67%
  #lut&reg                371   out of   1546   24.00%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    38
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1546   |614     |226     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1138   |305     |136     |927     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |20      |9       |22      |0       |0       |
|    demodu                  |Demodulation                                     |540    |128     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |14      |0       |26      |0       |0       |
|    integ                   |Integration                                      |139    |18      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |93     |39      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |311    |81      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |116    |100     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |25      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |20     |16      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |63     |59      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1453  
    #2          2       312   
    #3          3       110   
    #4          4        17   
    #5        5-10       81   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6618, tnet num: 2044, tinst num: 850, tnode num: 8960, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 850
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2046, pip num: 14846
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1311 valid insts, and 39430 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.129594s wall, 17.937500s user + 0.078125s system = 18.015625s CPU (575.7%)

RUN-1004 : used memory is 525 MB, reserved memory is 493 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_181622.log"
